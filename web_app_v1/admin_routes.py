#!/usr/bin/env python3
"""
Admin Routes for OAuth Federation Configuration

This module provides admin-only routes for configuring the OAuth federation system,
including Databricks connection settings, identity provider configuration, and
federation policy management.
"""

import os
import json
import requests
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, BooleanField, SubmitField, SelectField
from wtforms.validators import DataRequired, URL, Email, Length
from functools import wraps

from models import db, OrganizationConfig, User, AuditLog

# Create admin blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorator to require admin role"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Admin access required.', 'danger')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

class OrganizationConfigForm(FlaskForm):
    """Form for organization configuration"""
    # Databricks Configuration
    databricks_host = StringField('Databricks Host',
                                 validators=[DataRequired()],
                                 render_kw={"placeholder": "e.g., dbc-123abc45-6def.cloud.databricks.com"})
    databricks_account_id = StringField('Databricks Account ID (Optional)',
                                       render_kw={"placeholder": "e.g., ********-1234-1234-1234-************"})
    genie_space_id = StringField('Genie Space ID',
                                validators=[DataRequired()],
                                render_kw={"placeholder": "e.g., 12ab345cd6789000ef6a2fb844ba2d31?o=************3456"})

    # Identity Provider Configuration
    issuer_url = StringField('Identity Provider URL',
                            validators=[DataRequired(), URL()],
                            render_kw={"placeholder": "https://your-domain.com/oauth"})
    audience = StringField('Token Audience',
                          validators=[DataRequired()],
                          default='databricks',
                          render_kw={"placeholder": "databricks"})
    subject_claim = StringField('Subject Claim',
                               validators=[DataRequired()],
                               default='sub',
                               render_kw={"placeholder": "sub"})

    # Okta Configuration
    okta_domain = StringField('Okta Domain',
                              render_kw={"placeholder": "https://your-domain.okta.com"})
    okta_client_id = StringField('Okta Client ID',
                                render_kw={"placeholder": "0oa..."})
    okta_client_secret = StringField('Okta Client Secret',
                                    render_kw={"placeholder": "Client secret from Okta app"})

    # Actions
    generate_keys = BooleanField('Generate New RSA Key Pair')
    submit = SubmitField('Save Configuration')

class UserManagementForm(FlaskForm):
    """Form for user management"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    role = SelectField('Role', choices=[('user', 'User'), ('admin', 'Admin')], validators=[DataRequired()])
    databricks_username = StringField('Databricks Username (Optional)',
                                     render_kw={"placeholder": "Leave empty to use email"})
    submit = SubmitField('Create User')

@admin_bp.route('/')
@login_required
@admin_required
def dashboard():
    """Admin dashboard"""
    config = OrganizationConfig.get_current_config()
    user_count = User.query.count()
    admin_count = User.query.filter_by(role='admin').count()

    # Recent audit logs
    recent_logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).limit(10).all()

    return render_template('admin/dashboard.html',
                         config=config,
                         user_count=user_count,
                         admin_count=admin_count,
                         recent_logs=recent_logs)

@admin_bp.route('/config', methods=['GET', 'POST'])
@login_required
@admin_required
def config():
    """Organization configuration"""
    form = OrganizationConfigForm()
    current_config = OrganizationConfig.get_current_config()

    if form.validate_on_submit():
        try:
            # Create or update configuration
            if current_config:
                config_obj = current_config
            else:
                config_obj = OrganizationConfig()
                config_obj.created_by = current_user.id

            # Update basic configuration
            config_obj.databricks_host = form.databricks_host.data.strip()
            config_obj.databricks_account_id = form.databricks_account_id.data.strip() or None
            config_obj.genie_space_id = form.genie_space_id.data.strip()
            config_obj.issuer_url = form.issuer_url.data.strip()
            config_obj.audience = form.audience.data.strip()
            config_obj.subject_claim = form.subject_claim.data.strip()

            # Handle Okta configuration
            if form.okta_domain.data and form.okta_client_id.data and form.okta_client_secret.data:
                from models import ExternalIDPConfig

                # Get or create Okta IDP config
                okta_config = ExternalIDPConfig.query.filter_by(
                    organization_id=config_obj.organization_id,
                    provider_name='okta'
                ).first()

                if not okta_config:
                    okta_config = ExternalIDPConfig(
                        organization_id=config_obj.organization_id,
                        provider_name='okta',
                        provider_display_name='Okta SSO',
                        created_by=current_user.id
                    )

                # Update Okta configuration
                okta_config.client_id = form.okta_client_id.data.strip()
                okta_config.client_secret = form.okta_client_secret.data.strip()  # Should be encrypted in production
                okta_config.domain = form.okta_domain.data.strip()
                okta_config.is_active = True

                if not ExternalIDPConfig.query.filter_by(organization_id=config_obj.organization_id, provider_name='okta').first():
                    db.session.add(okta_config)

                flash('Okta configuration saved successfully!', 'success')

            # Generate new keys if requested or if no keys exist
            if form.generate_keys.data or not config_obj.private_key_pem:
                from auth_federation import jwt_provider
                private_key, public_key, key_id = jwt_provider.generate_rsa_keypair()
                config_obj.private_key_pem = private_key
                config_obj.public_key_pem = public_key
                config_obj.key_id = key_id
                flash('New RSA key pair generated successfully.', 'success')

            # Save configuration
            if not current_config:
                db.session.add(config_obj)
            db.session.commit()

            # Log configuration change
            AuditLog.log_action(
                user_id=current_user.id,
                action='CONFIG_UPDATED',
                resource='organization_config',
                details=f"Updated organization configuration",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash('Organization configuration saved successfully.', 'success')
            return redirect(url_for('admin.config'))

        except Exception as e:
            flash(f'Error saving configuration: {str(e)}', 'danger')

    # Pre-fill form with current configuration
    okta_config = None
    if current_config and request.method == 'GET':
        form.databricks_host.data = current_config.databricks_host
        form.databricks_account_id.data = current_config.databricks_account_id
        form.genie_space_id.data = current_config.genie_space_id
        form.issuer_url.data = current_config.issuer_url
        form.audience.data = current_config.audience
        form.subject_claim.data = current_config.subject_claim

        # Get Okta configuration if exists
        if current_config.organization_id:
            from models import ExternalIDPConfig
            okta_config = ExternalIDPConfig.query.filter_by(
                organization_id=current_config.organization_id,
                provider_name='okta'
            ).first()

            if okta_config:
                form.okta_domain.data = okta_config.domain
                form.okta_client_id.data = okta_config.client_id
                form.okta_client_secret.data = okta_config.client_secret

    return render_template('admin/config.html', form=form, config=current_config, okta_config=okta_config)

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """User management"""
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template('admin/users.html', users=users)

@admin_bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    """Create new user"""
    form = UserManagementForm()

    if form.validate_on_submit():
        try:
            # Check if user already exists
            existing_user = User.query.filter_by(email=form.email.data).first()
            if existing_user:
                flash('User with this email already exists.', 'danger')
                return render_template('admin/create_user.html', form=form)

            # Create new user
            user = User(
                email=form.email.data,
                role=form.role.data,
                databricks_username=form.databricks_username.data or form.email.data
            )

            # Set temporary password (user should change on first login)
            temp_password = 'TempPass123!'
            user.set_password(temp_password)

            db.session.add(user)
            db.session.commit()

            # Log user creation
            AuditLog.log_action(
                user_id=current_user.id,
                action='USER_CREATED',
                resource='user_management',
                details=f"Created user: {user.email} with role: {user.role}",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash(f'User created successfully. Temporary password: {temp_password}', 'success')
            return redirect(url_for('admin.users'))

        except Exception as e:
            flash(f'Error creating user: {str(e)}', 'danger')

    return render_template('admin/create_user.html', form=form)

@admin_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Toggle user active status"""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('Cannot deactivate your own account.', 'danger')
        return redirect(url_for('admin.users'))

    user.is_active = not user.is_active
    db.session.commit()

    # Log status change
    AuditLog.log_action(
        user_id=current_user.id,
        action='USER_STATUS_CHANGED',
        resource='user_management',
        details=f"{'Activated' if user.is_active else 'Deactivated'} user: {user.email}",
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    status = 'activated' if user.is_active else 'deactivated'
    flash(f'User {user.email} has been {status}.', 'success')
    return redirect(url_for('admin.users'))

@admin_bp.route('/federation/status')
@login_required
@admin_required
def federation_status():
    """Check federation policy status"""
    config = OrganizationConfig.get_current_config()
    if not config:
        return jsonify({'error': 'No configuration found'}), 400

    # TODO: Implement federation policy status check with Databricks API
    # This would require admin credentials to check the federation policy

    return jsonify({
        'configured': config.federation_policy_configured,
        'policy_id': config.federation_policy_id,
        'issuer_url': config.get_issuer_url(),
        'jwks_uri': config.get_jwks_uri()
    })

@admin_bp.route('/federation/setup-guide')
@login_required
@admin_required
def federation_setup_guide():
    """Show federation setup guide"""
    config = OrganizationConfig.get_current_config()
    if not config:
        flash('Please configure the organization settings first.', 'warning')
        return redirect(url_for('admin.config'))

    # Generate sample federation policy
    sample_policy = {
        "oidc_policy": {
            "issuer": config.get_issuer_url(),
            "audiences": [config.audience],
            "subject_claim": config.subject_claim
        }
    }

    return render_template('admin/federation_setup.html',
                         config=config,
                         sample_policy=json.dumps(sample_policy, indent=2))

@admin_bp.route('/audit-logs')
@login_required
@admin_required
def audit_logs():
    """View audit logs"""
    page = request.args.get('page', 1, type=int)
    logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).paginate(
        page=page, per_page=50, error_out=False
    )
    return render_template('admin/audit_logs.html', logs=logs)
