# 🔧 **Alternative API Federation Setup**

## 🎯 **The Permission Issue**

Your current PAT token has these permissions:
- ✅ **Account Info Access**: Can read basic account details
- ❌ **Federation Policy Management**: Cannot create/manage federation policies

## 🛠️ **Solution: Get Account Admin Token**

### **Option A: Create Account Admin PAT**
1. **Login to Account Console**: `https://accounts.cloud.databricks.com`
2. **Go to Settings**: Look for "Access tokens" or "API tokens"
3. **Create Account-level Token**: 
   - Name: "Federation Policy Management"
   - Scope: Account admin or federation management
   - Lifetime: 90 days

### **Option B: Use Service Principal**
1. **Create Service Principal**: In account console
2. **Grant Account Admin**: Give federation policy permissions
3. **Generate Token**: For the service principal
4. **Use in API**: Replace your current token

## 🔄 **Retry API Call with New Token**

Once you have an account admin token:

```bash
curl --request POST \
  --header "Authorization: Bearer YOUR_NEW_ACCOUNT_ADMIN_TOKEN" \
  --header "Content-Type: application/json" \
  "https://accounts.cloud.databricks.com/api/2.0/accounts/bdd3103b-f558-4e9d-b5f6-42152a7aec03/federation-policies" \
  --data '{
    "name": "U2M OAuth Federation Policy",
    "description": "OAuth federation policy for User-to-Machine authentication",
    "oidc_policy": {
      "issuer": "http://localhost:5001/oauth",
      "audiences": ["databricks"],
      "subject_claim": "sub",
      "email_claim": "email"
    }
  }'
```

## 🔍 **Verify Token Permissions**

Test your new token:
```bash
# Test account access
curl --header "Authorization: Bearer YOUR_NEW_TOKEN" \
  "https://accounts.cloud.databricks.com/api/2.0/accounts/bdd3103b-f558-4e9d-b5f6-42152a7aec03"

# Test federation policies list
curl --header "Authorization: Bearer YOUR_NEW_TOKEN" \
  "https://accounts.cloud.databricks.com/api/2.0/accounts/bdd3103b-f558-4e9d-b5f6-42152a7aec03/federation-policies"
```

## ⚠️ **Important Notes**

1. **Account vs Workspace**: Federation policies are account-level, not workspace-level
2. **Permissions**: Requires account admin or specific federation permissions
3. **Token Scope**: Workspace PATs may not work for account APIs
4. **Alternative**: Web interface is often easier for initial setup
