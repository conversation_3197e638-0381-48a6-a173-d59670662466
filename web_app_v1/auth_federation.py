#!/usr/bin/env python3
"""
OAuth Federation System for Databricks - U2M Implementation

This module implements JWT token creation and OAuth federation with Databricks
according to the OAuth federation documentation. It acts as an identity provider
that issues JWT tokens which are then exchanged for Databricks OAuth tokens.

Key Features:
- U2M (User-to-Machine) authentication preserving individual user identity
- Multi-tenant support for multiple organizations/clients
- External IDP integration (Google, Azure AD, etc.)
- Proper user identity mapping to Databricks usernames
"""

import os
import time
import json
import requests
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import jwt
import secrets
from flask import current_app, request
from models import db, OrganizationConfig, UserSession, AuditLog

class ExternalIDPProvider:
    """External Identity Provider Integration for U2M Authentication"""

    def __init__(self):
        self.supported_providers = {
            'google': {
                'auth_url': 'https://accounts.google.com/o/oauth2/v2/auth',
                'token_url': 'https://oauth2.googleapis.com/token',
                'userinfo_url': 'https://www.googleapis.com/oauth2/v2/userinfo',
                'scope': 'openid email profile'
            },
            'azure': {
                'auth_url': 'https://login.microsoftonline.com/{tenant}/oauth2/v2.0/authorize',
                'token_url': 'https://login.microsoftonline.com/{tenant}/oauth2/v2.0/token',
                'userinfo_url': 'https://graph.microsoft.com/v1.0/me',
                'scope': 'openid email profile'
            },
            'okta': {
                'auth_url': '{domain}/oauth2/v1/authorize',
                'token_url': '{domain}/oauth2/v1/token',
                'userinfo_url': '{domain}/oauth2/v1/userinfo',
                'scope': 'openid email profile'
            }
        }

    def get_authorization_url(self, provider, client_id, redirect_uri, state, domain=None, tenant=None):
        """Generate authorization URL for external IDP"""
        if provider not in self.supported_providers:
            raise ValueError(f"Unsupported provider: {provider}")

        config = self.supported_providers[provider].copy()

        # Handle provider-specific URL formatting
        if provider == 'azure' and tenant:
            config['auth_url'] = config['auth_url'].format(tenant=tenant)
        elif provider == 'okta' and domain:
            config['auth_url'] = config['auth_url'].format(domain=domain)

        # Generate PKCE parameters for security
        code_verifier = secrets.token_urlsafe(32)

        # Create code challenge from verifier
        import hashlib
        import base64
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

        params = {
            'client_id': client_id,
            'response_type': 'code',
            'redirect_uri': redirect_uri,
            'scope': config['scope'],
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256'
        }

        auth_url = config['auth_url'] + '?' + '&'.join([f"{k}={v}" for k, v in params.items()])

        return auth_url, code_verifier

    def exchange_code_for_token(self, provider, client_id, client_secret, code, redirect_uri, code_verifier, domain=None, tenant=None):
        """Exchange authorization code for access token"""
        if provider not in self.supported_providers:
            raise ValueError(f"Unsupported provider: {provider}")

        config = self.supported_providers[provider].copy()

        # Handle provider-specific URL formatting
        if provider == 'azure' and tenant:
            config['token_url'] = config['token_url'].format(tenant=tenant)
        elif provider == 'okta' and domain:
            config['token_url'] = config['token_url'].format(domain=domain)

        data = {
            'client_id': client_id,
            'client_secret': client_secret,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': redirect_uri,
            'code_verifier': code_verifier
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }

        response = requests.post(config['token_url'], data=data, headers=headers, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")

    def get_user_info(self, provider, access_token, domain=None):
        """Get user information from external IDP"""
        if provider not in self.supported_providers:
            raise ValueError(f"Unsupported provider: {provider}")

        config = self.supported_providers[provider].copy()

        # Handle provider-specific URL formatting
        if provider == 'okta' and domain:
            config['userinfo_url'] = config['userinfo_url'].format(domain=domain)

        headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json'
        }

        response = requests.get(config['userinfo_url'], headers=headers, timeout=30)

        if response.status_code == 200:
            user_info = response.json()

            # Normalize user info across providers
            normalized_info = {
                'email': user_info.get('email'),
                'name': user_info.get('name'),
                'given_name': user_info.get('given_name'),
                'family_name': user_info.get('family_name'),
                'picture': user_info.get('picture'),
                'sub': user_info.get('sub'),  # Subject identifier
                'provider': provider,
                'raw': user_info
            }

            return normalized_info
        else:
            raise Exception(f"Failed to get user info: {response.status_code} - {response.text}")

class JWTTokenProvider:
    """JWT Token Provider for OAuth Federation"""

    def __init__(self):
        self.config = None

    def _load_config(self):
        """Load organization configuration"""
        if self.config is None:
            self.config = OrganizationConfig.get_current_config()
            if not self.config:
                raise ValueError("Organization configuration not found. Please configure the system first.")

    def generate_rsa_keypair(self):
        """Generate RSA key pair for JWT signing"""
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )

        # Get public key
        public_key = private_key.public_key()

        # Serialize private key
        private_pem = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ).decode('utf-8')

        # Serialize public key
        public_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        ).decode('utf-8')

        # Generate key ID
        key_id = secrets.token_hex(16)

        return private_pem, public_pem, key_id

    def create_jwt_token(self, user, session_id=None, external_user_info=None):
        """Create JWT token for user with U2M authentication support

        Args:
            user: Local user object
            session_id: Session identifier
            external_user_info: User info from external IDP (for federated users)
        """
        if not self.config:
            self._load_config()

        # Token payload
        now = datetime.now(datetime.timezone.utc)
        exp = now + timedelta(hours=1)  # 1 hour expiration

        # Determine the subject (Databricks username) for U2M authentication
        # This ensures individual user identity is preserved in Databricks audit logs
        databricks_username = self._get_databricks_username(user, external_user_info)

        payload = {
            'iss': self.config.get_issuer_url(),  # Issuer (our identity provider)
            'aud': self.config.audience,          # Audience (databricks)
            'sub': databricks_username,          # Subject (Databricks username for U2M)
            'iat': int(now.timestamp()),          # Issued at
            'exp': int(exp.timestamp()),          # Expiration
            'jti': secrets.token_hex(16),         # JWT ID
            'email': user.email,                  # User email
            'role': user.role,                    # User role
            'session_id': session_id,             # Session ID
            'auth_type': 'u2m',                   # Authentication type (U2M vs M2M)
            'user_id': user.id,                   # Internal user ID
        }

        # Add external IDP information if available
        if external_user_info:
            payload.update({
                'idp_provider': external_user_info.get('provider'),
                'idp_sub': external_user_info.get('sub'),
                'idp_email': external_user_info.get('email'),
                'given_name': external_user_info.get('given_name'),
                'family_name': external_user_info.get('family_name')
            })

        # Load private key
        private_key = serialization.load_pem_private_key(
            self.config.private_key_pem.encode('utf-8'),
            password=None,
            backend=default_backend()
        )

        # Create JWT token
        token = jwt.encode(
            payload,
            private_key,
            algorithm='RS256',
            headers={'kid': self.config.key_id}
        )

        return token, exp

    def _get_databricks_username(self, user, external_user_info=None):
        """Determine the Databricks username for U2M authentication

        This method ensures proper user identity mapping for Databricks audit logs.
        Priority order:
        1. Explicitly set databricks_username in user profile
        2. Email from external IDP (for federated users)
        3. User's email address
        """
        # Check if user has explicitly mapped Databricks username
        if user.databricks_username:
            return user.databricks_username

        # For federated users, use the email from external IDP
        if external_user_info and external_user_info.get('email'):
            return external_user_info['email']

        # Fallback to user's email
        return user.email

    def _load_config(self):
        """Load organization configuration"""
        if self.config is None:
            self.config = OrganizationConfig.get_current_config()
            if not self.config:
                raise ValueError("Organization configuration not found. Please configure the system first.")

    def verify_jwt_token(self, token):
        """Verify JWT token"""
        if not self.config:
            self._load_config()

        try:
            # Load public key
            public_key = serialization.load_pem_public_key(
                self.config.public_key_pem.encode('utf-8'),
                backend=default_backend()
            )

            # Verify and decode token
            payload = jwt.decode(
                token,
                public_key,
                algorithms=['RS256'],
                audience=self.config.audience,
                issuer=self.config.get_issuer_url()
            )

            return payload
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid JWT token: {str(e)}")

    def get_jwks(self):
        """Get JSON Web Key Set for public key discovery"""
        if not self.config:
            self._load_config()

        # Load public key
        public_key = serialization.load_pem_public_key(
            self.config.public_key_pem.encode('utf-8'),
            backend=default_backend()
        )

        # Get public key numbers
        public_numbers = public_key.public_numbers()

        # Convert to base64url encoding
        def int_to_base64url(value):
            import base64
            byte_length = (value.bit_length() + 7) // 8
            value_bytes = value.to_bytes(byte_length, byteorder='big')
            return base64.urlsafe_b64encode(value_bytes).decode('ascii').rstrip('=')

        # Create JWKS
        jwks = {
            "keys": [
                {
                    "kty": "RSA",
                    "use": "sig",
                    "alg": "RS256",
                    "kid": self.config.key_id,
                    "n": int_to_base64url(public_numbers.n),
                    "e": int_to_base64url(public_numbers.e)
                }
            ]
        }

        return jwks

    def get_openid_configuration(self):
        """Get OpenID Provider Configuration"""
        if not self.config:
            self._load_config()

        base_url = self.config.get_issuer_url()

        return {
            "issuer": base_url,
            "jwks_uri": f"{base_url}/.well-known/jwks.json",
            "response_types_supported": ["id_token"],
            "subject_types_supported": ["public"],
            "id_token_signing_alg_values_supported": ["RS256"],
            "token_endpoint_auth_methods_supported": ["none"]
        }

class DatabricksFederationClient:
    """Client for Databricks OAuth Federation"""

    def __init__(self):
        self.config = None

    def _load_config(self):
        """Load organization configuration"""
        if self.config is None:
            self.config = OrganizationConfig.get_current_config()
            if not self.config:
                raise ValueError("Organization configuration not found. Please configure the system first.")

    def exchange_jwt_for_databricks_token(self, jwt_token, user_session):
        """Exchange JWT token for Databricks OAuth token using federation"""
        if not self.config:
            self._load_config()

        # Databricks token federation endpoint
        if self.config.databricks_account_id:
            # Account-level federation
            token_url = f"https://accounts.cloud.databricks.com/oidc/accounts/{self.config.databricks_account_id}/v1/token"
        else:
            # Workspace-level federation
            token_url = f"https://{self.config.databricks_host}/oidc/v1/token"

        # Prepare token exchange request
        data = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:token-exchange',
            'subject_token': jwt_token,
            'subject_token_type': 'urn:ietf:params:oauth:token-type:jwt',
            'scope': 'all-apis offline_access'
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }

        try:
            # Make token exchange request
            response = requests.post(
                token_url,
                data=data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                token_data = response.json()

                # Calculate expiration time
                expires_in = token_data.get('expires_in', 3600)
                expires_at = datetime.now(datetime.timezone.utc) + timedelta(seconds=expires_in)

                # Store tokens in user session
                user_session.databricks_access_token = token_data.get('access_token')
                user_session.databricks_refresh_token = token_data.get('refresh_token')
                user_session.databricks_token_expires_at = expires_at
                db.session.commit()

                # Log successful token exchange
                AuditLog.log_action(
                    user_id=user_session.user_id,
                    action='TOKEN_EXCHANGE_SUCCESS',
                    resource='databricks_oauth',
                    details=f"JWT exchanged for Databricks token",
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.headers.get('User-Agent') if request else None
                )

                return token_data
            else:
                error_msg = f"Token exchange failed: {response.status_code} - {response.text}"

                # Log failed token exchange
                AuditLog.log_action(
                    user_id=user_session.user_id,
                    action='TOKEN_EXCHANGE_FAILED',
                    resource='databricks_oauth',
                    details=error_msg,
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.headers.get('User-Agent') if request else None
                )

                raise Exception(error_msg)

        except requests.RequestException as e:
            error_msg = f"Network error during token exchange: {str(e)}"

            # Log network error
            AuditLog.log_action(
                user_id=user_session.user_id,
                action='TOKEN_EXCHANGE_ERROR',
                resource='databricks_oauth',
                details=error_msg,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None
            )

            raise Exception(error_msg)

    def refresh_databricks_token(self, user_session):
        """Refresh Databricks token using refresh token"""
        if not user_session.databricks_refresh_token:
            raise Exception("No refresh token available")

        if not self.config:
            self._load_config()

        # Databricks token endpoint
        if self.config.databricks_account_id:
            token_url = f"https://accounts.cloud.databricks.com/oidc/accounts/{self.config.databricks_account_id}/v1/token"
        else:
            token_url = f"https://{self.config.databricks_host}/oidc/v1/token"

        data = {
            'grant_type': 'refresh_token',
            'refresh_token': user_session.databricks_refresh_token
        }

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }

        try:
            response = requests.post(token_url, data=data, headers=headers, timeout=30)

            if response.status_code == 200:
                token_data = response.json()

                # Update tokens
                expires_in = token_data.get('expires_in', 3600)
                expires_at = datetime.now(datetime.timezone.utc) + timedelta(seconds=expires_in)

                user_session.databricks_access_token = token_data.get('access_token')
                if 'refresh_token' in token_data:
                    user_session.databricks_refresh_token = token_data['refresh_token']
                user_session.databricks_token_expires_at = expires_at
                db.session.commit()

                return token_data
            else:
                raise Exception(f"Token refresh failed: {response.status_code} - {response.text}")

        except requests.RequestException as e:
            raise Exception(f"Network error during token refresh: {str(e)}")

# Global instances
external_idp_provider = ExternalIDPProvider()
jwt_provider = JWTTokenProvider()
federation_client = DatabricksFederationClient()
