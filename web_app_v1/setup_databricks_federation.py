#!/usr/bin/env python3
"""
Databricks OAuth Federation Policy Setup Script

This script helps set up the OAuth federation policy in Databricks
for U2M (User-to-Machine) authentication using the REST API.
"""

import requests
import json
import base64
import getpass
from urllib.parse import urljoin

class DatabricksFederationSetup:
    def __init__(self, account_host, username, password):
        self.account_host = account_host.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        
        # Set up basic auth
        credentials = f"{username}:{password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        self.session.headers.update({
            'Authorization': f'Basic {encoded_credentials}',
            'Content-Type': 'application/json'
        })
    
    def test_connection(self):
        """Test connection to Databricks account"""
        try:
            url = f"{self.account_host}/api/2.0/accounts/me"
            response = self.session.get(url)
            
            if response.status_code == 200:
                account_info = response.json()
                print(f"✅ Connected to Databricks account: {account_info.get('username', 'Unknown')}")
                return True, account_info
            else:
                print(f"❌ Connection failed: {response.status_code} - {response.text}")
                return False, None
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False, None
    
    def list_workspaces(self):
        """List available workspaces"""
        try:
            url = f"{self.account_host}/api/2.0/accounts/workspaces"
            response = self.session.get(url)
            
            if response.status_code == 200:
                workspaces = response.json().get('workspaces', [])
                print(f"📋 Found {len(workspaces)} workspace(s):")
                for ws in workspaces:
                    print(f"   - {ws['workspace_name']} ({ws['deployment_name']})")
                    print(f"     ID: {ws['workspace_id']}")
                    print(f"     URL: {ws['workspace_url']}")
                return workspaces
            else:
                print(f"❌ Failed to list workspaces: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            print(f"❌ Error listing workspaces: {e}")
            return []
    
    def create_federation_policy(self, issuer_url, audience="databricks", subject_claim="sub"):
        """Create OAuth federation policy"""
        try:
            # Federation policy configuration
            policy_config = {
                "name": "U2M OAuth Federation Policy",
                "description": "OAuth federation policy for User-to-Machine authentication",
                "oidc_policy": {
                    "issuer": issuer_url,
                    "audiences": [audience],
                    "subject_claim": subject_claim,
                    "email_claim": "email",
                    "groups_claim": "groups"
                }
            }
            
            url = f"{self.account_host}/api/2.0/accounts/federation-policies"
            response = self.session.post(url, json=policy_config)
            
            if response.status_code == 200:
                policy = response.json()
                print(f"✅ Federation policy created successfully!")
                print(f"   Policy ID: {policy.get('policy_id', 'Unknown')}")
                print(f"   Issuer: {policy.get('oidc_policy', {}).get('issuer', 'Unknown')}")
                return True, policy
            else:
                print(f"❌ Failed to create federation policy: {response.status_code}")
                print(f"   Response: {response.text}")
                return False, None
        except Exception as e:
            print(f"❌ Error creating federation policy: {e}")
            return False, None
    
    def list_federation_policies(self):
        """List existing federation policies"""
        try:
            url = f"{self.account_host}/api/2.0/accounts/federation-policies"
            response = self.session.get(url)
            
            if response.status_code == 200:
                policies = response.json().get('policies', [])
                print(f"📋 Found {len(policies)} federation policy/policies:")
                for policy in policies:
                    print(f"   - {policy.get('name', 'Unnamed')}")
                    print(f"     ID: {policy.get('policy_id', 'Unknown')}")
                    print(f"     Issuer: {policy.get('oidc_policy', {}).get('issuer', 'Unknown')}")
                return policies
            else:
                print(f"❌ Failed to list federation policies: {response.status_code} - {response.text}")
                return []
        except Exception as e:
            print(f"❌ Error listing federation policies: {e}")
            return []

def main():
    print("🔐 Databricks OAuth Federation Policy Setup")
    print("=" * 50)
    
    # Get configuration
    account_host = input("Enter Databricks account host (e.g., https://accounts.cloud.databricks.com): ").strip()
    if not account_host.startswith('http'):
        account_host = f"https://{account_host}"
    
    username = input("Enter your Databricks username: ").strip()
    password = getpass.getpass("Enter your Databricks password: ")
    
    # Initialize setup
    setup = DatabricksFederationSetup(account_host, username, password)
    
    # Test connection
    print("\n🔍 Testing connection...")
    connected, account_info = setup.test_connection()
    if not connected:
        print("❌ Cannot proceed without valid connection")
        return
    
    # List workspaces
    print("\n📋 Listing workspaces...")
    workspaces = setup.list_workspaces()
    
    # List existing federation policies
    print("\n📋 Checking existing federation policies...")
    existing_policies = setup.list_federation_policies()
    
    # Get federation policy configuration
    print("\n⚙️  Federation Policy Configuration")
    print("For your U2M OAuth implementation, use these settings:")
    
    # Default issuer URL from your app
    default_issuer = "https://localhost:5001/oauth"
    issuer_url = input(f"Enter issuer URL [{default_issuer}]: ").strip() or default_issuer
    
    audience = input("Enter audience [databricks]: ").strip() or "databricks"
    subject_claim = input("Enter subject claim [sub]: ").strip() or "sub"
    
    # Create federation policy
    print(f"\n🔨 Creating federation policy...")
    print(f"   Issuer: {issuer_url}")
    print(f"   Audience: {audience}")
    print(f"   Subject Claim: {subject_claim}")
    
    confirm = input("\nProceed with creation? (y/N): ").strip().lower()
    if confirm == 'y':
        success, policy = setup.create_federation_policy(issuer_url, audience, subject_claim)
        
        if success:
            print("\n🎉 Federation policy setup complete!")
            print("\n📋 Next Steps:")
            print("1. Update your web app configuration with the policy details")
            print("2. Configure your external IDP (Google, Azure AD, etc.)")
            print("3. Test the U2M authentication flow")
            print("4. Verify user identity preservation in Databricks audit logs")
            
            # Save configuration
            config = {
                "federation_policy": policy,
                "issuer_url": issuer_url,
                "audience": audience,
                "subject_claim": subject_claim,
                "account_host": account_host
            }
            
            with open("databricks_federation_config.json", "w") as f:
                json.dump(config, f, indent=2)
            print(f"\n💾 Configuration saved to: databricks_federation_config.json")
        else:
            print("\n❌ Federation policy creation failed")
    else:
        print("\n⏹️  Federation policy creation cancelled")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
