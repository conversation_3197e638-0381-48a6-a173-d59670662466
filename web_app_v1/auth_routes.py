#!/usr/bin/env python3
"""
SSO Authentication Routes

This module provides SSO-style authentication routes for the OAuth federation system.
Users log in with email/password, and the system handles JWT token creation and
Databricks federation in the background.
"""

import os
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, ValidationError
from datetime import datetime

from models import db, User, UserSession, OrganizationConfig, AuditLog

# Create auth blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

class LoginForm(FlaskForm):
    """SSO Login Form"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = Bo<PERSON>anField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    """User Registration Form (for demo purposes)"""
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
    confirm_password = PasswordField('Confirm Password', validators=[DataRequired()])
    databricks_username = StringField('Databricks Username (Optional)',
                                     render_kw={"placeholder": "Leave empty to use email"})
    submit = SubmitField('Register')

    def validate_confirm_password(self, field):
        if field.data != self.password.data:
            raise ValidationError('Passwords must match.')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """SSO Login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.conversation'))

    form = LoginForm()

    if form.validate_on_submit():
        # Find user
        user = User.query.filter_by(email=form.email.data).first()

        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Your account has been deactivated. Please contact an administrator.', 'danger')
                return render_template('auth/sso_login.html', form=form)

            # Check if organization is configured
            config = OrganizationConfig.get_current_config()
            if not config:
                if user.is_admin():
                    flash('Please configure the organization settings first.', 'warning')
                    login_user(user, remember=form.remember_me.data)
                    return redirect(url_for('admin.config'))
                else:
                    flash('System not configured. Please contact an administrator.', 'danger')
                    return render_template('auth/sso_login.html', form=form)

            try:
                # Import federation modules here to avoid circular imports
                from auth_federation import jwt_provider, federation_client

                # Create user session
                user_session = UserSession.create_session(
                    user_id=user.id,
                    user_agent=request.headers.get('User-Agent'),
                    ip_address=request.remote_addr
                )

                # Create JWT token
                jwt_token, jwt_expires_at = jwt_provider.create_jwt_token(user, user_session.id)

                # Store JWT token in session
                user_session.jwt_token = jwt_token
                user_session.jwt_expires_at = jwt_expires_at
                db.session.commit()

                # Exchange JWT for Databricks token
                try:
                    federation_client.exchange_jwt_for_databricks_token(
                        jwt_token, user_session
                    )

                    # Login successful
                    login_user(user, remember=form.remember_me.data)
                    user.update_last_login()

                    # Store session token in Flask session
                    session['session_token'] = user_session.session_token

                    # Log successful login
                    AuditLog.log_action(
                        user_id=user.id,
                        action='LOGIN_SUCCESS',
                        resource='authentication',
                        details=f"SSO login successful with federation",
                        ip_address=request.remote_addr,
                        user_agent=request.headers.get('User-Agent')
                    )

                    flash('Login successful! Connected to Databricks.', 'success')

                    # Redirect to next page or conversation
                    next_page = request.args.get('next')
                    if next_page:
                        return redirect(next_page)
                    return redirect(url_for('main.conversation'))

                except Exception as e:
                    # JWT created but Databricks federation failed
                    # Still allow login but show warning
                    login_user(user, remember=form.remember_me.data)
                    user.update_last_login()
                    session['session_token'] = user_session.session_token

                    # Log federation failure
                    AuditLog.log_action(
                        user_id=user.id,
                        action='LOGIN_FEDERATION_FAILED',
                        resource='authentication',
                        details=f"Login successful but Databricks federation failed: {str(e)}",
                        ip_address=request.remote_addr,
                        user_agent=request.headers.get('User-Agent')
                    )

                    flash(f'Login successful, but Databricks connection failed: {str(e)}', 'warning')
                    return redirect(url_for('main.conversation'))

            except Exception as e:
                # JWT creation failed
                flash(f'Authentication system error: {str(e)}', 'danger')

                # Log system error
                AuditLog.log_action(
                    user_id=user.id,
                    action='LOGIN_SYSTEM_ERROR',
                    resource='authentication',
                    details=f"JWT creation failed: {str(e)}",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

                return render_template('auth/sso_login.html', form=form)
        else:
            # Invalid credentials
            flash('Invalid email or password.', 'danger')

            # Log failed login attempt
            if user:
                AuditLog.log_action(
                    user_id=user.id,
                    action='LOGIN_FAILED',
                    resource='authentication',
                    details=f"Invalid password attempt",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
            else:
                AuditLog.log_action(
                    user_id=None,
                    action='LOGIN_FAILED',
                    resource='authentication',
                    details=f"Invalid email attempt: {form.email.data}",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )

    return render_template('auth/sso_login.html', form=form)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User Registration (for demo purposes)"""
    # Check if registration is allowed
    config = OrganizationConfig.get_current_config()
    if not config:
        flash('System not configured. Registration is disabled.', 'danger')
        return redirect(url_for('auth.login'))

    form = RegistrationForm()

    if form.validate_on_submit():
        # Check if user already exists
        existing_user = User.query.filter_by(email=form.email.data).first()
        if existing_user:
            flash('Email already registered. Please use a different email.', 'danger')
            return render_template('auth/register.html', form=form)

        try:
            # Create new user
            user = User(
                email=form.email.data,
                role='user',  # Default role
                databricks_username=form.databricks_username.data or form.email.data
            )
            user.set_password(form.password.data)

            db.session.add(user)
            db.session.commit()

            # Log user registration
            AuditLog.log_action(
                user_id=user.id,
                action='USER_REGISTERED',
                resource='authentication',
                details=f"User self-registered",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash('Registration successful! You can now log in.', 'success')
            return redirect(url_for('auth.login'))

        except Exception as e:
            flash(f'Registration failed: {str(e)}', 'danger')

    return render_template('auth/register.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    """Logout"""
    # Invalidate user session
    session_token = session.get('session_token')
    if session_token:
        user_session = UserSession.get_by_token(session_token)
        if user_session:
            user_session.invalidate()

    # Log logout
    AuditLog.log_action(
        user_id=current_user.id,
        action='LOGOUT',
        resource='authentication',
        details=f"User logged out",
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    # Clear Flask session
    session.clear()

    # Logout user
    logout_user()

    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    """User profile"""
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change password"""
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if not all([current_password, new_password, confirm_password]):
        flash('All fields are required.', 'danger')
        return redirect(url_for('auth.profile'))

    if not current_user.check_password(current_password):
        flash('Current password is incorrect.', 'danger')
        return redirect(url_for('auth.profile'))

    if new_password != confirm_password:
        flash('New passwords do not match.', 'danger')
        return redirect(url_for('auth.profile'))

    if len(new_password) < 8:
        flash('Password must be at least 8 characters long.', 'danger')
        return redirect(url_for('auth.profile'))

    # Update password
    current_user.set_password(new_password)
    db.session.commit()

    # Log password change
    AuditLog.log_action(
        user_id=current_user.id,
        action='PASSWORD_CHANGED',
        resource='authentication',
        details=f"User changed password",
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    flash('Password changed successfully.', 'success')
    return redirect(url_for('auth.profile'))

# OAuth Identity Provider Endpoints
@auth_bp.route('/oauth')
def oauth_provider():
    """OAuth Identity Provider Information"""
    try:
        from auth_federation import jwt_provider
        config = OrganizationConfig.get_current_config()
        if not config:
            return jsonify({'error': 'Identity provider not configured'}), 500

        return jsonify({
            'issuer': config.get_issuer_url(),
            'status': 'active',
            'endpoints': {
                'authorization': f"{config.get_issuer_url()}/oauth/authorize",
                'token': f"{config.get_issuer_url()}/oauth/token",
                'jwks': f"{config.get_issuer_url()}/.well-known/jwks.json",
                'openid_configuration': f"{config.get_issuer_url()}/.well-known/openid-configuration"
            },
            'supported_grant_types': ['authorization_code', 'urn:ietf:params:oauth:grant-type:token-exchange'],
            'supported_response_types': ['code', 'id_token'],
            'supported_scopes': ['openid', 'profile', 'email']
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/oauth/authorize')
def oauth_authorize():
    """OAuth Authorization Endpoint"""
    # This is a simplified OAuth authorization endpoint
    # In a full implementation, this would handle OAuth authorization flows
    try:
        config = OrganizationConfig.get_current_config()
        if not config:
            return jsonify({'error': 'Identity provider not configured'}), 500

        # For now, return information about the OAuth provider
        return jsonify({
            'message': 'OAuth Authorization Endpoint',
            'issuer': config.get_issuer_url(),
            'status': 'ready',
            'note': 'This endpoint is configured for Databricks OAuth federation'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/oauth/token', methods=['POST'])
def oauth_token():
    """OAuth Token Endpoint"""
    # This endpoint would handle token exchange in a full OAuth implementation
    try:
        config = OrganizationConfig.get_current_config()
        if not config:
            return jsonify({'error': 'Identity provider not configured'}), 500

        return jsonify({
            'message': 'OAuth Token Endpoint',
            'issuer': config.get_issuer_url(),
            'status': 'ready',
            'supported_grant_types': ['urn:ietf:params:oauth:grant-type:token-exchange']
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Well-known endpoints for OpenID Connect discovery
@auth_bp.route('/.well-known/openid-configuration')
def openid_configuration():
    """OpenID Provider Configuration"""
    try:
        from auth_federation import jwt_provider
        config = jwt_provider.get_openid_configuration()
        return jsonify(config)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/.well-known/jwks.json')
def jwks():
    """JSON Web Key Set"""
    try:
        from auth_federation import jwt_provider
        jwks = jwt_provider.get_jwks()
        return jsonify(jwks)
    except Exception as e:
        return jsonify({'error': str(e)}), 500
