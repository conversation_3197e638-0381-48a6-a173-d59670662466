#!/usr/bin/env python3
"""
Simple Database Reset Script

This script removes the old database and lets the app create a fresh one
with the correct U2M OAuth federation schema.
"""

import os
import shutil
from datetime import datetime

def reset_database():
    """Reset database by removing old files"""
    
    print("🔄 Resetting Database for U2M OAuth Federation")
    print("=" * 50)
    
    # List of possible database files to remove
    db_files = [
        "genie_federation.db",
        "instance/genie.db",
        "genie.db"
    ]
    
    removed_files = []
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Create backup first
                backup_name = f"{db_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(db_file, backup_name)
                print(f"✅ Backed up {db_file} to {backup_name}")
                
                # Remove original
                os.remove(db_file)
                print(f"✅ Removed {db_file}")
                removed_files.append(db_file)
                
            except Exception as e:
                print(f"⚠️  Could not remove {db_file}: {e}")
        else:
            print(f"ℹ️  {db_file} not found")
    
    # Also remove any SQLite journal files
    journal_files = [f"{db}.journal" for db in db_files] + [f"{db}-journal" for db in db_files]
    for journal_file in journal_files:
        if os.path.exists(journal_file):
            try:
                os.remove(journal_file)
                print(f"✅ Removed journal file: {journal_file}")
            except Exception as e:
                print(f"⚠️  Could not remove {journal_file}: {e}")
    
    if removed_files:
        print(f"\n🎉 Database reset complete! Removed {len(removed_files)} database file(s)")
    else:
        print("\n✨ No database files found to remove")
    
    print("\n📋 Next Steps:")
    print("1. Run: python app.py")
    print("2. The app will create a fresh database with U2M schema")
    print("3. Login with the default admin credentials")
    print("4. Configure your Databricks settings")
    
    return True

if __name__ == "__main__":
    reset_database()
