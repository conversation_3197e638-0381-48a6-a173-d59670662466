#!/usr/bin/env python3
"""
Database Models for OAuth Federation System - U2M Multi-tenant Implementation

This module defines the database models for user management, organization configuration,
and OAuth federation settings for the Databricks Genie Web Application.

Key Features:
- Multi-tenant support for multiple organizations/clients
- External IDP integration (Google, Azure AD, etc.)
- U2M (User-to-Machine) authentication support
- Individual user identity preservation
"""

from datetime import datetime, timedelta, timezone
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
import secrets

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """User model for SSO authentication"""
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='user')  # 'admin' or 'user'
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime)

    # Databricks user mapping for U2M authentication
    databricks_username = db.Column(db.String(120), nullable=True)  # Maps to Databricks user

    # External IDP integration
    external_idp_provider = db.Column(db.String(50), nullable=True)  # google, azure, okta, etc.
    external_idp_subject = db.Column(db.String(255), nullable=True)  # Subject ID from external IDP
    external_idp_email = db.Column(db.String(120), nullable=True)    # Email from external IDP

    # Multi-tenant support
    organization_id = db.Column(db.Integer, db.ForeignKey('organizations.id'), nullable=True)

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        """Check if user is admin"""
        return self.role == 'admin'

    def get_databricks_username(self):
        """Get Databricks username for JWT token"""
        return self.databricks_username or self.email

    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.now(timezone.utc)
        db.session.commit()

    def __repr__(self):
        return f'<User {self.email}>'

class Organization(db.Model):
    """Organization/Client model for multi-tenant support"""
    __tablename__ = 'organizations'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(120), nullable=False)
    domain = db.Column(db.String(120), nullable=True)  # Organization domain
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Contact information
    contact_email = db.Column(db.String(120), nullable=True)
    contact_name = db.Column(db.String(120), nullable=True)

    # Metadata
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc),
                          onupdate=lambda: datetime.now(timezone.utc))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    users = db.relationship('User', backref='organization', foreign_keys='User.organization_id')
    configs = db.relationship('OrganizationConfig', backref='organization', foreign_keys='OrganizationConfig.organization_id')
    idp_configs = db.relationship('ExternalIDPConfig', backref='organization', foreign_keys='ExternalIDPConfig.organization_id')

    def __repr__(self):
        return f'<Organization {self.name}>'

class ExternalIDPConfig(db.Model):
    """External Identity Provider Configuration for U2M Authentication"""
    __tablename__ = 'external_idp_configs'

    id = db.Column(db.Integer, primary_key=True)
    organization_id = db.Column(db.Integer, db.ForeignKey('organizations.id'), nullable=False)

    # IDP Configuration
    provider_name = db.Column(db.String(50), nullable=False)  # google, azure, okta, etc.
    provider_display_name = db.Column(db.String(100), nullable=False)

    # OAuth Configuration
    client_id = db.Column(db.String(255), nullable=False)
    client_secret = db.Column(db.String(255), nullable=False)  # Should be encrypted in production

    # Provider-specific settings
    tenant_id = db.Column(db.String(255), nullable=True)      # For Azure AD
    domain = db.Column(db.String(255), nullable=True)         # For Okta

    # Configuration
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    auto_create_users = db.Column(db.Boolean, default=True, nullable=False)
    default_role = db.Column(db.String(20), default='user', nullable=False)

    # User mapping configuration
    email_claim = db.Column(db.String(50), default='email', nullable=False)
    name_claim = db.Column(db.String(50), default='name', nullable=False)
    given_name_claim = db.Column(db.String(50), default='given_name', nullable=False)
    family_name_claim = db.Column(db.String(50), default='family_name', nullable=False)

    # Metadata
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc),
                          onupdate=lambda: datetime.now(timezone.utc))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    def get_redirect_uri(self, base_url):
        """Get the OAuth redirect URI for this IDP"""
        return f"{base_url}/auth/external/{self.provider_name}/callback"

    def __repr__(self):
        return f'<ExternalIDPConfig {self.provider_display_name}>'

class OrganizationConfig(db.Model):
    """Organization configuration for Databricks connection - Enhanced for Multi-tenant U2M"""
    __tablename__ = 'organization_config'

    id = db.Column(db.Integer, primary_key=True)
    organization_id = db.Column(db.Integer, db.ForeignKey('organizations.id'), nullable=True)  # Multi-tenant support

    # Databricks Configuration
    databricks_host = db.Column(db.String(255), nullable=False)
    databricks_account_id = db.Column(db.String(100), nullable=True)
    genie_space_id = db.Column(db.String(255), nullable=False)

    # Identity Provider Configuration
    issuer_url = db.Column(db.String(255), nullable=False)  # Our identity provider URL
    audience = db.Column(db.String(255), nullable=False, default='databricks')
    subject_claim = db.Column(db.String(50), nullable=False, default='sub')

    # JWT Signing Configuration
    private_key_pem = db.Column(db.Text, nullable=False)  # RSA private key for signing JWTs
    public_key_pem = db.Column(db.Text, nullable=False)   # RSA public key for verification
    key_id = db.Column(db.String(50), nullable=False)     # Key ID for JWKS

    # Federation Policy Configuration
    federation_policy_configured = db.Column(db.Boolean, default=False)
    federation_policy_id = db.Column(db.String(100), nullable=True)

    # Metadata
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc),
                          onupdate=lambda: datetime.now(timezone.utc))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Relationships
    creator = db.relationship('User', backref='created_configs')

    @classmethod
    def get_current_config(cls):
        """Get the current organization configuration"""
        return cls.query.first()

    def get_issuer_url(self):
        """Get the issuer URL for JWT tokens"""
        return self.issuer_url

    def get_jwks_uri(self):
        """Get the JWKS URI for public key discovery"""
        return f"{self.issuer_url}/.well-known/jwks.json"

    def get_openid_config_uri(self):
        """Get the OpenID configuration URI"""
        return f"{self.issuer_url}/.well-known/openid-configuration"

    def __repr__(self):
        return f'<OrganizationConfig {self.databricks_host}>'

class UserSession(db.Model):
    """User session management for JWT tokens"""
    __tablename__ = 'user_sessions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_token = db.Column(db.String(255), unique=True, nullable=False, index=True)

    # JWT Token Information
    jwt_token = db.Column(db.Text, nullable=True)  # Our internal JWT token
    jwt_expires_at = db.Column(db.DateTime, nullable=True)

    # Databricks Token Information (from federation)
    databricks_access_token = db.Column(db.Text, nullable=True)
    databricks_refresh_token = db.Column(db.Text, nullable=True)
    databricks_token_expires_at = db.Column(db.DateTime, nullable=True)

    # Session Metadata
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_used = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    is_active = db.Column(db.Boolean, default=True)
    user_agent = db.Column(db.String(500), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)

    # Relationships
    user = db.relationship('User', backref='sessions')

    @classmethod
    def create_session(cls, user_id, user_agent=None, ip_address=None):
        """Create a new user session"""
        session_token = secrets.token_urlsafe(32)
        session = cls(
            user_id=user_id,
            session_token=session_token,
            user_agent=user_agent,
            ip_address=ip_address
        )
        db.session.add(session)
        db.session.commit()
        return session

    @classmethod
    def get_by_token(cls, session_token):
        """Get session by token"""
        return cls.query.filter_by(session_token=session_token, is_active=True).first()

    def update_last_used(self):
        """Update last used timestamp"""
        self.last_used = datetime.now(timezone.utc)
        db.session.commit()

    def is_jwt_valid(self):
        """Check if JWT token is still valid"""
        if not self.jwt_token or not self.jwt_expires_at:
            return False
        return datetime.now(timezone.utc) < self.jwt_expires_at

    def is_databricks_token_valid(self):
        """Check if Databricks token is still valid"""
        if not self.databricks_access_token or not self.databricks_token_expires_at:
            return False
        # Add 60 second buffer
        return datetime.now(timezone.utc) < (self.databricks_token_expires_at - timedelta(seconds=60))

    def invalidate(self):
        """Invalidate the session"""
        self.is_active = False
        db.session.commit()

    def __repr__(self):
        return f'<UserSession {self.user.email}>'

class AuditLog(db.Model):
    """Audit log for tracking user actions"""
    __tablename__ = 'audit_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    action = db.Column(db.String(100), nullable=False)
    resource = db.Column(db.String(255), nullable=True)
    details = db.Column(db.Text, nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(500), nullable=True)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), index=True)

    # Relationships
    user = db.relationship('User', backref='audit_logs')

    @classmethod
    def log_action(cls, user_id, action, resource=None, details=None, ip_address=None, user_agent=None):
        """Log a user action"""
        log_entry = cls(
            user_id=user_id,
            action=action,
            resource=resource,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(log_entry)
        db.session.commit()
        return log_entry

    def __repr__(self):
        return f'<AuditLog {self.action} by {self.user.email if self.user else "Unknown"}>'

def init_db(app):
    """Initialize database with Flask app"""
    db.init_app(app)

    with app.app_context():
        # Create all tables
        db.create_all()

        # Create default admin user if none exists
        if not User.query.filter_by(role='admin').first():
            admin_user = User(
                email='<EMAIL>',
                role='admin',
                databricks_username='<EMAIL>'
            )
            admin_user.set_password('admin123')  # Change this in production!
            db.session.add(admin_user)
            db.session.commit()
            print("Created default admin user: <EMAIL> / admin123")
