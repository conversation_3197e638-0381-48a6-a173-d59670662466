#!/usr/bin/env python3
"""
Databricks Federation Policy Setup Script

This script helps create federation policies in Databricks using OAuth credentials
instead of PAT tokens, which are required for federation policy management.
"""

import requests
import json
import base64
from urllib.parse import urlencode, parse_qs, urlparse
import webbrowser
import time
from models import OrganizationConfig

class DatabricksFederationSetup:
    """Helper class for setting up Databricks federation policies"""
    
    def __init__(self):
        self.config = OrganizationConfig.get_current_config()
        if not self.config:
            raise Exception("No organization configuration found. Please configure the system first.")
    
    def get_oauth_authorization_url(self, client_id, redirect_uri, scopes="all-apis"):
        """Generate OAuth authorization URL for Databricks"""
        if self.config.databricks_account_id:
            # Account-level OAuth
            auth_url = f"https://accounts.cloud.databricks.com/oidc/accounts/{self.config.databricks_account_id}/v1/authorize"
        else:
            # Workspace-level OAuth
            auth_url = f"https://{self.config.databricks_host}/oidc/v1/authorize"
        
        params = {
            'client_id': client_id,
            'response_type': 'code',
            'redirect_uri': redirect_uri,
            'scope': scopes,
            'state': 'federation_setup'
        }
        
        return f"{auth_url}?{urlencode(params)}"
    
    def exchange_code_for_token(self, client_id, client_secret, code, redirect_uri):
        """Exchange authorization code for access token"""
        if self.config.databricks_account_id:
            # Account-level token endpoint
            token_url = f"https://accounts.cloud.databricks.com/oidc/accounts/{self.config.databricks_account_id}/v1/token"
        else:
            # Workspace-level token endpoint
            token_url = f"https://{self.config.databricks_host}/oidc/v1/token"
        
        # Prepare credentials
        credentials = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
        
        headers = {
            'Authorization': f'Basic {credentials}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': redirect_uri
        }
        
        response = requests.post(token_url, headers=headers, data=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")
    
    def create_federation_policy(self, access_token, issuer_url=None, audiences=None, subject_claim='sub'):
        """Create federation policy using OAuth access token"""
        if not issuer_url:
            issuer_url = self.config.get_issuer_url()
        
        if not audiences:
            audiences = [self.config.audience]
        
        # Federation policy endpoint
        if self.config.databricks_account_id:
            policy_url = f"https://accounts.cloud.databricks.com/api/2.0/accounts/{self.config.databricks_account_id}/federationPolicies"
        else:
            policy_url = f"https://{self.config.databricks_host}/api/2.0/federationPolicies"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        policy_data = {
            "oidc_policy": {
                "issuer": issuer_url,
                "audiences": audiences,
                "subject_claim": subject_claim
            }
        }
        
        response = requests.post(policy_url, headers=headers, json=policy_data)
        
        if response.status_code in [200, 201]:
            return response.json()
        else:
            raise Exception(f"Federation policy creation failed: {response.status_code} - {response.text}")
    
    def get_federation_policies(self, access_token):
        """List existing federation policies"""
        if self.config.databricks_account_id:
            policy_url = f"https://accounts.cloud.databricks.com/api/2.0/accounts/{self.config.databricks_account_id}/federationPolicies"
        else:
            policy_url = f"https://{self.config.databricks_host}/api/2.0/federationPolicies"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get(policy_url, headers=headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to get federation policies: {response.status_code} - {response.text}")

def interactive_setup():
    """Interactive setup for federation policy"""
    print("🔐 Databricks Federation Policy Setup")
    print("=" * 50)
    
    try:
        setup = DatabricksFederationSetup()
        config = setup.config
        
        print(f"📍 Databricks Host: {config.databricks_host}")
        if config.databricks_account_id:
            print(f"🏢 Account ID: {config.databricks_account_id}")
        print(f"🔗 Issuer URL: {config.get_issuer_url()}")
        print(f"👥 Audience: {config.audience}")
        print()
        
        # Step 1: Get OAuth credentials
        print("Step 1: OAuth Application Setup")
        print("-" * 30)
        print("You need to create an OAuth application in Databricks first:")
        if config.databricks_account_id:
            print(f"1. Go to: https://accounts.cloud.databricks.com/")
            print("2. Navigate to Settings → App connections")
        else:
            print(f"1. Go to: https://{config.databricks_host}/")
            print("2. Navigate to Settings → Developer → OAuth")
        print("3. Create a new OAuth application")
        print("4. Set redirect URI to: http://localhost:8080/callback")
        print("5. Copy the Client ID and Client Secret")
        print()
        
        client_id = input("Enter OAuth Client ID: ").strip()
        client_secret = input("Enter OAuth Client Secret: ").strip()
        
        if not client_id or not client_secret:
            print("❌ Client ID and Secret are required!")
            return
        
        # Step 2: OAuth Authorization
        print("\nStep 2: OAuth Authorization")
        print("-" * 30)
        
        redirect_uri = "http://localhost:8080/callback"
        auth_url = setup.get_oauth_authorization_url(client_id, redirect_uri)
        
        print(f"Opening browser for OAuth authorization...")
        print(f"If browser doesn't open, go to: {auth_url}")
        
        try:
            webbrowser.open(auth_url)
        except:
            pass
        
        print("\nAfter authorizing, you'll be redirected to a URL like:")
        print("http://localhost:8080/callback?code=AUTHORIZATION_CODE&state=federation_setup")
        print()
        
        callback_url = input("Paste the full callback URL here: ").strip()
        
        # Extract authorization code
        parsed_url = urlparse(callback_url)
        query_params = parse_qs(parsed_url.query)
        
        if 'code' not in query_params:
            print("❌ No authorization code found in URL!")
            return
        
        auth_code = query_params['code'][0]
        
        # Step 3: Exchange code for token
        print("\nStep 3: Token Exchange")
        print("-" * 30)
        
        try:
            token_data = setup.exchange_code_for_token(client_id, client_secret, auth_code, redirect_uri)
            access_token = token_data['access_token']
            print("✅ Successfully obtained access token!")
        except Exception as e:
            print(f"❌ Token exchange failed: {e}")
            return
        
        # Step 4: Create federation policy
        print("\nStep 4: Create Federation Policy")
        print("-" * 30)
        
        try:
            # Check existing policies first
            try:
                existing_policies = setup.get_federation_policies(access_token)
                if existing_policies.get('policies'):
                    print("📋 Existing federation policies:")
                    for policy in existing_policies['policies']:
                        print(f"  - Policy ID: {policy.get('policy_id')}")
                        print(f"    Issuer: {policy.get('oidc_policy', {}).get('issuer')}")
                    print()
            except Exception as e:
                print(f"⚠️  Could not list existing policies: {e}")
            
            # Create new policy
            policy_result = setup.create_federation_policy(
                access_token,
                issuer_url=config.get_issuer_url(),
                audiences=[config.audience],
                subject_claim=config.subject_claim
            )
            
            policy_id = policy_result.get('policy_id')
            print(f"✅ Federation policy created successfully!")
            print(f"📋 Policy ID: {policy_id}")
            
            # Update configuration
            config.federation_policy_configured = True
            config.federation_policy_id = policy_id
            
            from models import db
            db.session.commit()
            
            print("✅ Configuration updated in database!")
            
        except Exception as e:
            print(f"❌ Federation policy creation failed: {e}")
            return
        
        print("\n🎉 Federation setup completed successfully!")
        print("=" * 50)
        print("Your U2M OAuth federation is now configured and ready to use.")
        print("Users can now authenticate with individual identity preservation.")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")

if __name__ == "__main__":
    interactive_setup()
