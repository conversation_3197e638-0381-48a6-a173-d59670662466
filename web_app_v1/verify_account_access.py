#!/usr/bin/env python3
"""
Verify Databricks Account Access and Permissions
"""
import requests

def test_account_access():
    print("🔍 Testing Databricks Account Access")
    print("=" * 50)
    
    # Your credentials
    token = "************************************"
    account_id = "bdd3103b-f558-4e9d-b5f6-42152a7aec03"
    
    # Test 1: Check if we can access account info
    print("\n1️⃣ Testing Account Info Access...")
    url = f"https://accounts.cloud.databricks.com/api/2.0/accounts/{account_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Account access successful")
            print(f"   Account: {response.json()}")
        else:
            print(f"   ❌ Account access failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: List existing federation policies
    print("\n2️⃣ Testing Federation Policies List...")
    url = f"https://accounts.cloud.databricks.com/api/2.0/accounts/{account_id}/federation-policies"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Federation policies access successful")
            policies = response.json()
            print(f"   Existing policies: {len(policies.get('federation_policies', []))}")
        else:
            print(f"   ❌ Federation policies access failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check workspace access with same token
    print("\n3️⃣ Testing Workspace Access...")
    # You'll need to replace this with your actual workspace URL
    workspace_url = "https://dbc-bdd3103b-f558.cloud.databricks.com"  # Example
    url = f"{workspace_url}/api/2.0/clusters/list"
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Workspace access successful")
        else:
            print(f"   ❌ Workspace access failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("📋 Diagnosis:")
    print("   - If Account Info fails: Token lacks account admin permissions")
    print("   - If Federation Policies fails: Feature not enabled or wrong permissions")
    print("   - If Workspace succeeds but Account fails: Token is workspace-only")

if __name__ == "__main__":
    test_account_access()
