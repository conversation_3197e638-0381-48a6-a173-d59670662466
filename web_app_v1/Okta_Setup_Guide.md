# 🔐 **Okta Identity Provider Setup for U2M Authentication**

## 🎯 **Overview**
This guide sets up <PERSON><PERSON> as your external identity provider for U2M (User-to-Machine) authentication, enabling corporate SSO with individual user identity preservation in Databricks.

## 📋 **Step 1: Create Okta Application**

### **1.1 Access Okta Admin Console**
1. Go to your Okta admin console: `https://your-domain.okta.com/admin`
2. Login with your Okta admin credentials

### **1.2 Create New Application**
1. Navigate to **Applications** → **Applications**
2. Click **Create App Integration**
3. Select **OIDC - OpenID Connect**
4. Choose **Web Application**
5. Click **Next**

### **1.3 Configure Application Settings**
- **App integration name**: `Databricks Genie U2M`
- **Grant type**: ✅ Authorization Code, ✅ Refresh Token
- **Sign-in redirect URIs**: 
  ```
  http://localhost:5001/auth/external/okta/callback
  ```
- **Sign-out redirect URIs**: 
  ```
  http://localhost:5001/auth/logout
  ```
- **Controlled access**: Choose appropriate user/group assignment

### **1.4 Save and Note Credentials**
After creating the app, note these values:
- **Client ID**: `0oa...` (copy this)
- **Client Secret**: Click "Show" and copy
- **Okta Domain**: `https://your-domain.okta.com`

## 📋 **Step 2: Configure User Attributes**

### **2.1 Set Up Claims (Optional but Recommended)**
1. Go to **Security** → **API** → **Authorization Servers**
2. Select **default** authorization server
3. Go to **Claims** tab
4. Add custom claims if needed:
   - **groups**: For role mapping
   - **department**: For organizational mapping

### **2.2 Assign Users/Groups**
1. Go back to your application
2. Click **Assignments** tab
3. Assign users or groups who should have access
This is the discussion by my manager:

(0:00) For example, whatever login, the first login that you will see is like admin. (0:05) Admin will have, admin can manage a multiple client where they will configure the Databricks host, workspace, genie workspace and everything. (0:15) They will be configured there.
(0:17) So we will be managing multiple clients from that admin. (0:21) Okay, that's one thing. (0:23) That configuration will be done there.
(0:24) But when now user is going to use this, like whatever our application. (0:32) So at that time what will happen, they will go for another login. (0:35) This is admin part.
(0:38) Okay, when they go for like login part, from there they do some login. (0:45) Okay, using some OAuth, like we will use another OAuth or something. (0:49) We will do a login for that user.
(0:51) That user get logged in to application. (0:55) In background what it is going to do, in background it is going to use this federation. (1:02) Like federation token that will connect using that host and genie and everything.
(1:07) It will connect to that workspace. (1:10) It will validate whether that is a correct user. (1:13) Because like we have to see user and we have to generate token for that user.
(1:19) Okay. (1:20) Do you understand what I am saying? (1:22) So this federation doesn't need any way. (1:25) Yes, that will be in background itself.
(1:27) Even from the admin side? (1:30) Admin side, we will just give configuration that this is what workspace, this is what genie workspace and this is what host. (1:38) This is sort of information we will provide. (1:43) Okay, then so for that user, whatever user like we are logged in to our system.
(1:50) For example, I get paresh.tutip.com. (1:54) That is what like I get. (1:55) But for that user only we have to log in to Databricks. (1:59) We have to get the token for Paresh, not like Prasanna.
(2:04) Or like if Prasanna is logged in to the system, so I should get a token for Prasanna, not Paresh. (2:10) Okay. (2:10) Right now, what do you like when you have given this one? (2:14) So as like it is not giving more specific user.
(2:17) Is it being for specific user? (2:20) It's giving like login failed for this user. (2:24) It was giving like that thing. (2:26) But how login failed? (2:27) Which user, right? (2:28) So now which user it is trying to log in? (2:30) I gave my credential.
(2:32) It showed my name, whatever about Prasanna, Tutip.com. (2:36) Failing to log in. (2:38) Where you have given your credential? (2:41) This one in my application. (2:42) Admin only? (2:44) I logged in through admin and configured my credential.
(2:51) And later I tried to log in as a user instead of admin. (2:56) But do you understand that what I am trying to say right now? (2:59) After this, whatever user is logged in, either it is Prasanna or Paresh. (3:03) So Prasanna logged in.
(3:05) So Prasanna should, like Databricks token should get for Prasanna only, not for Paresh. (3:11) Though I am owner of workspace. (3:13) Do you understand? (3:15) That's why we are using that ID in between.
(3:23) Okay. (3:23) So let's again see. (3:26) We will be able to, we are going to create using this one only.
(3:29) This fast, this project can create fast using this only. (3:32) Using this, whatever like we are using here. (3:36) But it's just like we really need to understand what will be the flow.
(3:40) And what all things like we need. (3:43) So maybe we will first set up that IDP only. (3:46) Let's set up the IDP first part.
(3:50) Using IDP, we will access for a user tokens. (3:53) Whatever user we are providing for that user, like we will get a token access token. (3:58) Using IDP.
(3:59) That part we will do. (4:01) Then later we will do this admin part. (4:05) Do you understand what I am saying? (4:07) First IDP.
(4:09) Let's separate these two things. (4:11) Do you have that diagram? (4:15) The one I told you in call. (4:23) See.
(4:24) We will do this part later. (4:27) For example, Prasanna is logging in. (4:31) Prasanna token from IDP.
(4:33) Connecting IDP with database. (4:37) It will give me Prasanna token. (4:39) So what I am saying, we will work on this first.
(4:42) From our IDP, we will get a JSON based token. (4:45) We will get that particular user token only. (4:48) So if I have Paresh here, it should give me the Paresh token.
(4:51) If I have Prasanna here, it should give me the Prasanna token. (4:56) Understood? (4:57) And this part we will do later. (4:59) Okay.
(5:02) Right? (5:04) So later we will have UI around it. (5:07) Like where that particular user is going to log in. (5:10) Using OAuth or SSO.
(5:13) And what will happen? (5:15) This user, as soon as it logs in, we will get to know what is that user. (5:21) Right? (5:21) And we will ask for token for that user. (5:24) And it will give me token for that user.
(5:30) I will provide you the codes. (5:35) Everything whatever I did. (5:38) But understand, right? (5:40) Just don't work blindly.
(5:42) It would be better if some already experienced person deal. (5:49) See, that is fine. (5:51) It's fine.
(5:51) And you are on right track. (5:54) Like where you are doing the... (5:56) Using the Charjee PTA and creating all this stuff. (5:59) That is fine.
(6:02) Don't think it is not correct. (6:04) Like this is correct. (6:06) It might be correct, you know, but further... (6:09) No, but like that's what... (6:10) See, that's what I am saying.
(6:12) Like I try to understand. (6:15) Okay? (6:15) Try to understand that part, no? (6:17) Even some technology also could not understand. (6:20) That is fine.
(6:23) Try to understand. (6:24) Like go ahead, read about it and you will understand those things. (6:28) Okay? (6:29) I can understand if I have time, but... (6:32) We have to show all the time.
(6:34) That is... (6:35) See, we will be able to nail it. (6:38) Let's not worry about it. (6:41) Sorry, you are so cool now, but... (6:45) When you... (6:46) Like near to Monday, then... (6:49) Let's not worry.
(6:51) See... (6:52) If you... (6:53) Whenever in life you suddenly... (6:55) You will be in a hurry, right? (6:57) Like, what I will do, I will do, I will... (6:59) Nothing will go wrong. (7:02) Stay calm. (7:03) Think.
(7:04) And then implement. (7:05) Everything will be get done by that time. (7:09) Okay? (7:10) So don't be in that... (7:11) How it is going to be, how it is going to be.
(7:14) Nothing will happen. (7:15) Nothing will happen. (7:15) Like we will be on zero then again.
(7:20) Understood like what I am trying to say? (7:23) Yeah. (7:23) Okay? (7:25) So... (7:26) Don't... (7:27) Don't... (7:27) That's fine. (7:28) Like we will be able to do.
(7:30) Understand the things and then we will be able to do it. (7:33) See... (7:33) Without understanding, you are able to do this in two days. (7:38) Okay? (7:39) If you understand the concept, you will be able to do that within a half day only.
(7:45) Because now you know the exact thing. (7:48) Do you understand what I am trying to say? (7:50) You know exactly what you want and you will be implementing that using chartGPT or this AI itself. (7:56) You will be finish that within a half day only.
(8:00) Do you understand what I am trying to say? (8:03) Sir, but understanding... (8:05) That's what like... (8:06) That's where the important... (8:08) That's why I am pushing you to understand things. (8:12) Rather than just like... (8:14) How do I do it? (8:17) Understand things. (8:20) Okay? (8:20) So you are saying... (8:22) This UI part and frontend we can implement later.
(8:26) We have to just... (8:27) Yeah, like for now we will just keep the config file. (8:29) We will set it in the config file. (8:31) Directly the URL.
(8:33) We will set it. (8:34) We will put the frontend later. (8:37) Let's do that one.
(8:39) Like it's not something difficult or something. (8:41) It's just written here. (8:44) You enter username and password.
(8:46) It will identify and generate JWT. (8:50) This thing only is written here. (8:52) Nothing else.
(8:54) And you are saying many things. (8:56) What many things? (8:58) He is not saying many things, right? (9:00) So what he was saying... (9:02) This is what the part you guys have done. (9:05) Okay? (9:05) Now using this ID provider... (9:09) IDP... (9:09) Your IDP and all that stuff... (9:11) You will get a JSON token.
(9:13) Instead of using this service principle... (9:15) And client secret or something... (9:17) Because this will give for that account. (9:20) So you won't be able to understand which user is. (9:23) So he was asking to get the particular user access token.
(9:27) That is what we need to implement. (9:29) So that like when we are calling subsequent requests... (9:32) It will basically... (9:35) It will go against that user. (9:38) Understood? (9:39) Whatever like we are performing... (9:41) That will go against that user.
(9:42) Rather than any other user. (9:47) Understood? (9:49) See... (9:49) Can you see this? (9:51) This where like we will get a token. (9:54) After that Geny conversion API will be there.
(9:56) Query... (9:57) This will go to serverless... (9:59) This is what their flow already going on. (10:01) From here... (10:02) This is Geny. (10:03) Okay? (10:06) Up to here... (10:07) Like we have to provide an access token.
(10:10) So access token... (10:11) Here... (10:12) The authorisation layer... (10:14) We need user rather than machine. (10:17) The previous implementation which we did... (10:20) In that like we are getting the... (10:23) Machine... (10:24) His access token layer. (10:26) Do you understand now what I am trying to say? (10:29) This is what Databricks authorisation server is.
(10:31) So it has an user information... (10:33) Who can access this... (10:35) Or like that sort of thing. (10:36) Okay? (10:37) So from here... (10:38) We just want to get that particular user access token. (10:42) Not the service one.
(10:44) Understood? (10:45) Okay. (10:46) Understood that flow now? (10:48) But where is mentioned U2M? (10:51) This one... (10:52) He has created for M2M. (10:55) Instead of this... (10:56) We will have U2M here.
(10:58) Okay. (11:00) Okay. (11:00) Understood? (11:04) Okay.
(11:06) So what's wrong with my UI as of now? (11:13) With UI... (11:15) See, first thing I... (11:18) That's what... (11:19) UI like will come... (11:21) UI I don't think... (11:22) Something is wrong or something. (11:25) We will basically... (11:26) Require an admin who will log in. (11:29) That will configure for different organisation.
(11:32) Okay? (11:33) So you are just right now taking it for one organisation. (11:36) Right? (11:36) For example... (11:37) Can you put... (11:38) Log in... (11:45) You... (11:45) Do you have credential for use? (11:48) Hmm? (11:49) Because mine is not yet configured. (11:51) I don't have... (11:52) Credential.
(11:53) I am just... (11:54) I... (11:54) Whenever I log in... (11:55) I get a... (11:56) Email like that. (11:59) Like your TOTIP email I am asking. (12:02) Hmm.
(12:13) Login must fail, right? (12:14) Now if I... (12:16) Login through admin... (12:18) Credential... (12:21) It says... (12:25) But this is your local, right? (12:27) You are checking that from database. (12:32) How will this work here? (12:36) If you are checking this from database... (12:39) How that is going to work that order? (12:45) You understood what I am trying to say? (13:04) No, but this is local, right? (13:07) This activity is local. (13:09) This should be storing in your database.
(13:11) Which database are you using? (13:16) Non-DB... (13:21) Understand, baba. (13:23) Don't... (13:24) Okay? (13:25) Honestly. (13:31) Okay? (13:32) You think first.
(13:35) Just... (13:36) Don't do this. (13:39) Hi, Rashmi. (13:44) What are you doing, boy? (14:20) Hi, Mahiru.
(14:21) Hi, Mahiru. (14:26) Okay? (14:27) Think and then work again. (14:30) It will be completed in half a day.
(14:33) Okay? (14:37) Sir, like... (14:38) I suggest that if... (14:43) If you need anything, you come to me. (14:45) If you need anything, you come to me. (14:47) But first, try to understand.
(14:49) Like I explained you right now the architecture, right? (14:51) How it is going to be. (14:53) Accordingly, we have to implement. (14:54) That's what I am saying.
(14:55) First, finish the IDP configuration. (14:59) From IDP to database, we are getting the actual user. (15:03) Then, we will finish the first part.


implement accordingly what is required

for reference: doc1 - https://docs.databricks.com/aws/en/dev-tools/auth/oauth-u2m
doc2  - https://docs.databricks.com/aws/en/dev-tools/auth/oauth-federation

and the attached flow charts as well 
## 📋 **Step 3: Test Okta Configuration**

### **3.1 Test Authentication Flow**
1. Go to your app's **General** tab
2. Copy the **Okta domain** URL
3. Test the authorization URL:
   ```
   https://your-domain.okta.com/oauth2/v1/authorize?client_id=YOUR_CLIENT_ID&response_type=code&scope=openid%20email%20profile&redirect_uri=http://localhost:5001/auth/external/okta/callback&state=test
   ```

## 🔧 **Step 4: Configure Web Application**

### **4.1 Required Information**
You'll need these values for the web app configuration:
- **Okta Domain**: `https://your-domain.okta.com`
- **Client ID**: From step 1.4
- **Client Secret**: From step 1.4
- **Redirect URI**: `http://localhost:5001/auth/external/okta/callback`

### **4.2 User Mapping**
Configure how Okta users map to Databricks users:
- **Email Claim**: `email` (default)
- **Name Claim**: `name` (default)
- **Given Name**: `given_name` (default)
- **Family Name**: `family_name` (default)
- **Groups Claim**: `groups` (if configured)

## ⚠️ **Important Notes**

### **Security Considerations**
- Use HTTPS in production (not http://localhost)
- Rotate client secrets regularly
- Limit user access appropriately
- Enable MFA in Okta for additional security

### **Production URLs**
For production deployment, update redirect URIs to:
```
https://your-domain.com/auth/external/okta/callback
https://your-domain.com/auth/logout
```

## 🎯 **Benefits of Okta Integration**

### **For Users**
- ✅ Single Sign-On with corporate credentials
- ✅ No additional passwords to remember
- ✅ Seamless authentication experience

### **For IT/Security**
- ✅ Centralized user management
- ✅ MFA enforcement capability
- ✅ Audit logging and compliance
- ✅ Group-based access control

### **For Databricks**
- ✅ Individual user identity preservation
- ✅ Real user names in audit logs
- ✅ Per-user access control
- ✅ Compliance with regulatory requirements
