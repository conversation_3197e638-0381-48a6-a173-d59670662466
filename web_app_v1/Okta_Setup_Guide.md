# 🔐 **Okta Identity Provider Setup for U2M Authentication**

## 🎯 **Overview**
This guide sets up <PERSON><PERSON> as your external identity provider for U2M (User-to-Machine) authentication, enabling corporate SSO with individual user identity preservation in Databricks.

## 📋 **Step 1: Create Okta Application**

### **1.1 Access Okta Admin Console**
1. Go to your Okta admin console: `https://your-domain.okta.com/admin`
2. Login with your Okta admin credentials

### **1.2 Create New Application**
1. Navigate to **Applications** → **Applications**
2. Click **Create App Integration**
3. Select **OIDC - OpenID Connect**
4. Choose **Web Application**
5. Click **Next**

### **1.3 Configure Application Settings**
- **App integration name**: `Databricks Genie U2M`
- **Grant type**: ✅ Authorization Code, ✅ Refresh Token
- **Sign-in redirect URIs**: 
  ```
  http://localhost:5001/auth/external/okta/callback
  ```
- **Sign-out redirect URIs**: 
  ```
  http://localhost:5001/auth/logout
  ```
- **Controlled access**: Choose appropriate user/group assignment

### **1.4 Save and Note Credentials**
After creating the app, note these values:
- **Client ID**: `0oa...` (copy this)
- **Client Secret**: Click "Show" and copy
- **Okta Domain**: `https://your-domain.okta.com`

## 📋 **Step 2: Configure User Attributes**

### **2.1 Set Up Claims (Optional but Recommended)**
1. Go to **Security** → **API** → **Authorization Servers**
2. Select **default** authorization server
3. Go to **Claims** tab
4. Add custom claims if needed:
   - **groups**: For role mapping
   - **department**: For organizational mapping

### **2.2 Assign Users/Groups**
1. Go back to your application
2. Click **Assignments** tab
3. Assign users or groups who should have access

## 📋 **Step 3: Test Okta Configuration**

### **3.1 Test Authentication Flow**
1. Go to your app's **General** tab
2. Copy the **Okta domain** URL
3. Test the authorization URL:
   ```
   https://your-domain.okta.com/oauth2/v1/authorize?client_id=YOUR_CLIENT_ID&response_type=code&scope=openid%20email%20profile&redirect_uri=http://localhost:5001/auth/external/okta/callback&state=test
   ```

## 🔧 **Step 4: Configure Web Application**

### **4.1 Required Information**
You'll need these values for the web app configuration:
- **Okta Domain**: `https://your-domain.okta.com`
- **Client ID**: From step 1.4
- **Client Secret**: From step 1.4
- **Redirect URI**: `http://localhost:5001/auth/external/okta/callback`

### **4.2 User Mapping**
Configure how Okta users map to Databricks users:
- **Email Claim**: `email` (default)
- **Name Claim**: `name` (default)
- **Given Name**: `given_name` (default)
- **Family Name**: `family_name` (default)
- **Groups Claim**: `groups` (if configured)

## ⚠️ **Important Notes**

### **Security Considerations**
- Use HTTPS in production (not http://localhost)
- Rotate client secrets regularly
- Limit user access appropriately
- Enable MFA in Okta for additional security

### **Production URLs**
For production deployment, update redirect URIs to:
```
https://your-domain.com/auth/external/okta/callback
https://your-domain.com/auth/logout
```

## 🎯 **Benefits of Okta Integration**

### **For Users**
- ✅ Single Sign-On with corporate credentials
- ✅ No additional passwords to remember
- ✅ Seamless authentication experience

### **For IT/Security**
- ✅ Centralized user management
- ✅ MFA enforcement capability
- ✅ Audit logging and compliance
- ✅ Group-based access control

### **For Databricks**
- ✅ Individual user identity preservation
- ✅ Real user names in audit logs
- ✅ Per-user access control
- ✅ Compliance with regulatory requirements
