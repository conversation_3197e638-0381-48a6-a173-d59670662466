#!/usr/bin/env python3
"""
Complete Federation Setup Fix

This script provides multiple approaches to fix Databricks federation setup
since the standard endpoints are not working.
"""

import requests
import json
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import OrganizationConfig

def test_workspace_endpoints(host, token):
    """Test various Databricks workspace endpoints to find federation support"""
    
    endpoints_to_test = [
        # Standard federation endpoints
        f"https://{host}/api/2.0/federation-policies",
        f"https://{host}/api/2.0/federationPolicies", 
        
        # Workspace configuration endpoints
        f"https://{host}/api/2.0/workspace-conf",
        f"https://{host}/api/2.0/workspace/conf",
        
        # Identity provider endpoints
        f"https://{host}/api/2.0/identity-providers",
        f"https://{host}/api/2.0/idp",
        
        # OAuth endpoints
        f"https://{host}/api/2.0/oauth",
        f"https://{host}/api/2.0/oauth/applications",
        
        # Service principal endpoints
        f"https://{host}/api/2.0/service-principals",
        f"https://{host}/api/2.0/preview/scim/v2/ServicePrincipals",
        
        # Account-level endpoints (might work from workspace)
        f"https://{host}/api/2.0/accounts/federation-policies",
        
        # Unity Catalog endpoints
        f"https://{host}/api/2.1/unity-catalog/federation-policies",
        
        # Preview/experimental endpoints
        f"https://{host}/api/2.0/preview/federation-policies",
        f"https://{host}/api/2.1/federation-policies"
    ]
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("🔍 Testing Databricks API Endpoints...")
    print("=" * 50)
    
    working_endpoints = []
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(endpoint, headers=headers, timeout=10)
            status = response.status_code
            
            if status == 200:
                print(f"✅ {endpoint} - Working (200)")
                working_endpoints.append((endpoint, 'GET', 200))
            elif status == 404:
                print(f"❌ {endpoint} - Not Found (404)")
            elif status == 403:
                print(f"🔒 {endpoint} - Forbidden (403) - Might need different permissions")
                working_endpoints.append((endpoint, 'GET', 403))
            elif status == 401:
                print(f"🔑 {endpoint} - Unauthorized (401) - Check token")
            elif status == 405:
                print(f"📝 {endpoint} - Method Not Allowed (405) - Try POST")
                # Try POST for endpoints that might only accept POST
                try:
                    post_response = requests.post(endpoint, headers=headers, json={}, timeout=10)
                    if post_response.status_code != 404:
                        print(f"📝 {endpoint} - POST returns {post_response.status_code}")
                        working_endpoints.append((endpoint, 'POST', post_response.status_code))
                except:
                    pass
            else:
                print(f"⚠️  {endpoint} - Status {status}")
                working_endpoints.append((endpoint, 'GET', status))
                
        except requests.exceptions.Timeout:
            print(f"⏱️  {endpoint} - Timeout")
        except requests.exceptions.ConnectionError:
            print(f"🔌 {endpoint} - Connection Error")
        except Exception as e:
            print(f"❓ {endpoint} - Error: {str(e)[:50]}")
    
    return working_endpoints

def try_workspace_configuration(host, token, issuer, audience, subject_claim):
    """Try to configure federation through workspace configuration"""
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Try workspace configuration approach
    workspace_conf_data = {
        "enableOidcFederation": "true",
        "oidcIssuer": issuer,
        "oidcAudience": audience,
        "oidcSubjectClaim": subject_claim
    }
    
    print("\n🔧 Trying Workspace Configuration Approach...")
    print("-" * 50)
    
    try:
        response = requests.patch(
            f"https://{host}/api/2.0/workspace-conf",
            headers=headers,
            json=workspace_conf_data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Workspace configuration updated successfully!")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"❌ Workspace configuration failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Workspace configuration error: {e}")
        return False

def create_service_principal_oauth(host, token):
    """Try to create service principal with OAuth capabilities"""
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("\n🔧 Trying Service Principal OAuth Creation...")
    print("-" * 50)
    
    # Create service principal
    sp_data = {
        "displayName": "Genie U2M Federation Service Principal",
        "active": True
    }
    
    try:
        response = requests.post(
            f"https://{host}/api/2.0/service-principals",
            headers=headers,
            json=sp_data,
            timeout=30
        )
        
        if response.status_code == 201:
            sp_info = response.json()
            sp_id = sp_info.get('id')
            print(f"✅ Service Principal created: {sp_id}")
            
            # Try to create OAuth secret
            secret_response = requests.post(
                f"https://{host}/api/2.0/service-principals/{sp_id}/credentials/secrets",
                headers=headers,
                json={"description": "OAuth secret for U2M federation"},
                timeout=30
            )
            
            if secret_response.status_code == 201:
                secret_info = secret_response.json()
                print(f"✅ OAuth secret created")
                print(f"Client ID: {secret_info.get('client_id', 'Not provided')}")
                print(f"Client Secret: {secret_info.get('client_secret', 'Not provided')}")
                return sp_id, secret_info
            else:
                print(f"❌ OAuth secret creation failed: {secret_response.status_code}")
                print(f"Response: {secret_response.text}")
                return sp_id, None
                
        else:
            print(f"❌ Service Principal creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Service Principal creation error: {e}")
        return None, None

def main():
    """Main function to fix federation setup"""
    
    print("🔧 Databricks Federation Setup Fix")
    print("=" * 50)
    
    # Get your PAT token
    token = input("Enter your Databricks Personal Access Token: ").strip()
    if not token:
        print("❌ Token is required")
        return
    
    with app.app_context():
        config = OrganizationConfig.get_current_config()
        if not config:
            print("❌ No organization configuration found. Please configure the system first.")
            return
        
        host = config.databricks_host
        issuer = config.get_issuer_url()
        audience = config.audience
        subject_claim = config.subject_claim
        
        print(f"📋 Configuration:")
        print(f"   Host: {host}")
        print(f"   Issuer: {issuer}")
        print(f"   Audience: {audience}")
        print(f"   Subject Claim: {subject_claim}")
        print()
        
        # Test endpoints
        working_endpoints = test_workspace_endpoints(host, token)
        
        if working_endpoints:
            print(f"\n✅ Found {len(working_endpoints)} potentially working endpoints:")
            for endpoint, method, status in working_endpoints:
                print(f"   {method} {endpoint} - Status {status}")
        
        # Try workspace configuration
        workspace_success = try_workspace_configuration(host, token, issuer, audience, subject_claim)
        
        # Try service principal approach
        sp_id, oauth_info = create_service_principal_oauth(host, token)
        
        # Provide manual instructions
        print("\n📋 MANUAL SETUP INSTRUCTIONS")
        print("=" * 50)
        print()
        print("Since automated federation setup is complex, here are manual steps:")
        print()
        print("1. **Contact Databricks Support**:")
        print("   - Open a support ticket")
        print("   - Request: Enable OAuth federation for workspace")
        print(f"   - Workspace: {host}")
        print(f"   - Issuer: {issuer}")
        print(f"   - Audience: {audience}")
        print()
        print("2. **Check Workspace Admin Console**:")
        print("   - Go to workspace as admin")
        print("   - Check: Settings → Admin Console")
        print("   - Look for: Identity and Access → External Identity Providers")
        print()
        print("3. **Use Account Console** (if you have account admin access):")
        print("   - Go to: https://accounts.cloud.databricks.com/")
        print("   - Navigate to: Settings → Identity and access")
        print("   - Look for: Federation or OAuth settings")
        print()
        print("4. **Alternative: Use Personal Access Token**:")
        print("   - For testing, you can use PAT tokens directly")
        print("   - Configure the app to use PAT instead of OAuth federation")
        print("   - This bypasses the federation requirement")
        
        if workspace_success:
            print("\n🎉 Workspace configuration was successful!")
            print("Try testing the OAuth flow now.")
        elif sp_id:
            print(f"\n🎉 Service Principal created: {sp_id}")
            print("You may be able to use this for OAuth authentication.")
        else:
            print("\n⚠️  Automated setup failed. Please follow manual instructions above.")

if __name__ == "__main__":
    main()
