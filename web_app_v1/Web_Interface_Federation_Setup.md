# 🌐 **Databricks Web Interface Federation Setup**

## 🎯 **Step-by-Step Guide**

### **Step 1: Access Account Console**
1. Go to: `https://accounts.cloud.databricks.com`
2. Login with your account admin credentials
3. Ensure you have account admin permissions

### **Step 2: Navigate to Federation**
1. Click on **"Identity & Access"** in the left sidebar
2. Select **"Federation"** from the submenu
3. You should see the federation policies management page

### **Step 3: Create Federation Policy**
1. Click **"Add Federation Policy"** or **"Create Policy"**
2. Fill in the following details:

#### **Basic Information:**
- **Name**: `U2M OAuth Federation Policy`
- **Description**: `OAuth federation policy for User-to-Machine authentication preserving individual user identity`

#### **OIDC Configuration:**
- **Issuer**: `http://localhost:5001/oauth`
- **Audience**: `databricks`
- **Subject Claim**: `sub`
- **Email <PERSON>laim**: `email`
- **Groups Claim**: `groups` (optional)

### **Step 4: Save and Note Policy ID**
1. Click **"Create"** or **"Save"**
2. **IMPORTANT**: Copy the Policy ID from the response
3. You'll need this ID for your web app configuration

### **Step 5: Configure Web App**
1. Go to: `http://localhost:5001/admin/config`
2. Login with: `<EMAIL> / admin123`
3. Enter the Federation Policy ID you just created
4. Save the configuration

## 🔍 **Troubleshooting**

### **If you don't see "Federation" option:**
- Ensure you have account admin permissions
- Contact your Databricks account administrator
- Federation feature might not be enabled for your account

### **If creation fails:**
- Check that the issuer URL is accessible
- Verify all required fields are filled
- Ensure you have proper permissions

## ✅ **Success Verification**
After creating the policy, you should see:
- Policy listed in the federation policies page
- A unique Policy ID (save this!)
- Status showing as "Active" or "Enabled"

## 🚀 **Next Steps**
1. Configure your web app with the Policy ID
2. Test the complete U2M authentication flow
3. Verify individual user identity in Databricks audit logs
