#!/usr/bin/env python3
"""
Federation API Helper

This module provides API helper functions for making Databricks Genie API calls
using the OAuth federation system. It handles token management and API requests
with proper error handling and token refresh.
"""

import os
import time
import requests
from datetime import datetime, timedelta, timezone
from flask import session, request
from flask_login import current_user

from models import db, UserSession, OrganizationConfig, AuditLog

class FederationAPIClient:
    """API client for Databricks Genie using OAuth federation"""

    def __init__(self):
        self.config = None

    def _load_config(self):
        """Load organization configuration"""
        if self.config is None:
            self.config = OrganizationConfig.get_current_config()
            if not self.config:
                raise ValueError("Organization configuration not found")

    def _get_user_session(self):
        """Get current user session"""
        if not current_user.is_authenticated:
            raise ValueError("User not authenticated")

        # Try to get existing session
        user_session = UserSession.query.filter_by(
            user_id=current_user.id,
            is_active=True
        ).first()

        # Create new session if none exists
        if not user_session:
            user_session = UserSession.create_session(
                user_id=current_user.id,
                user_agent=request.headers.get('User-Agent') if request else None,
                ip_address=request.remote_addr if request else None
            )

        return user_session

    def _ensure_valid_databricks_token(self, user_session):
        """Ensure user has a valid Databricks token"""
        # For demonstration purposes, return a mock token
        # In production, this would implement the full OAuth federation flow

        # Check if we have a valid Databricks token
        if user_session.is_databricks_token_valid():
            return user_session.databricks_access_token

        # Create a mock token for demonstration
        mock_token = f"mock_databricks_token_for_{current_user.email}_{int(time.time())}"

        # Update user session with mock token
        user_session.databricks_access_token = mock_token
        user_session.databricks_token_expires_at = datetime.now(timezone.utc) + timedelta(hours=1)
        db.session.commit()

        # Log token creation
        AuditLog.log_action(
            user_id=current_user.id,
            action='MOCK_TOKEN_CREATED',
            resource='federation_api',
            details=f"Created mock Databricks token for demonstration",
            ip_address=request.remote_addr if request else None,
            user_agent=request.headers.get('User-Agent') if request else None
        )

        return mock_token

    def get_auth_headers(self):
        """Get authorization headers for Databricks API requests"""
        user_session = self._get_user_session()
        token = self._ensure_valid_databricks_token(user_session)

        # Update last used timestamp
        user_session.update_last_used()

        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

    def get_genie_space_url(self):
        """Get the URL for the Genie space"""
        if not self.config:
            self._load_config()

        host = self.config.databricks_host
        space_id = self.config.genie_space_id

        # Extract space ID and org ID if present
        if "?" in space_id:
            space_id_part = space_id.split("?")[0]
            org_id_part = space_id.split("?")[1]
            if "o=" in org_id_part:
                org_id = org_id_part.split("o=")[1]
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}?o={org_id}"
            else:
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}"
        else:
            return f"https://{host}/api/2.0/genie/spaces/{space_id}"

    def get_start_conversation_url(self):
        """Get the URL for starting a conversation"""
        if not self.config:
            self._load_config()

        host = self.config.databricks_host
        space_id = self.config.genie_space_id

        # Extract space ID and org ID if present
        if "?" in space_id:
            space_id_part = space_id.split("?")[0]
            org_id_part = space_id.split("?")[1]
            if "o=" in org_id_part:
                org_id = org_id_part.split("o=")[1]
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/start-conversation?o={org_id}"
            else:
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/start-conversation"
        else:
            return f"https://{host}/api/2.0/genie/spaces/{space_id}/start-conversation"

    def get_send_message_url(self, conversation_id):
        """Get the URL for sending a message to a conversation"""
        if not self.config:
            self._load_config()

        host = self.config.databricks_host
        space_id = self.config.genie_space_id

        # Extract space ID and org ID if present
        if "?" in space_id:
            space_id_part = space_id.split("?")[0]
            org_id_part = space_id.split("?")[1]
            if "o=" in org_id_part:
                org_id = org_id_part.split("o=")[1]
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/conversations/{conversation_id}/messages?o={org_id}"
            else:
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/conversations/{conversation_id}/messages"
        else:
            return f"https://{host}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages"

    def get_message_url(self, conversation_id, message_id):
        """Get the URL for retrieving a message"""
        if not self.config:
            self._load_config()

        host = self.config.databricks_host
        space_id = self.config.genie_space_id

        # Extract space ID and org ID if present
        if "?" in space_id:
            space_id_part = space_id.split("?")[0]
            org_id_part = space_id.split("?")[1]
            if "o=" in org_id_part:
                org_id = org_id_part.split("o=")[1]
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/conversations/{conversation_id}/messages/{message_id}?o={org_id}"
            else:
                return f"https://{host}/api/2.0/genie/spaces/{space_id_part}/conversations/{conversation_id}/messages/{message_id}"
        else:
            return f"https://{host}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}"

    def test_connection(self):
        """Test the connection to Databricks Genie API"""
        try:
            headers = self.get_auth_headers()
            url = self.get_genie_space_url()

            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                return True, "Connection successful"
            elif response.status_code == 401:
                return False, "Authentication failed"
            elif response.status_code == 403:
                return False, "Permission denied"
            elif response.status_code == 404:
                return False, "Genie space not found"
            else:
                return False, f"Error: {response.status_code} - {response.text}"

        except Exception as e:
            return False, f"Connection error: {str(e)}"

    def start_conversation(self, question):
        """Start a new conversation with Genie"""
        try:
            headers = self.get_auth_headers()
            url = self.get_start_conversation_url()

            response = requests.post(
                url,
                headers=headers,
                json={'content': question},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                # Log successful API call
                AuditLog.log_action(
                    user_id=current_user.id,
                    action='GENIE_CONVERSATION_STARTED',
                    resource='genie_api',
                    details=f"Started conversation with question: {question[:100]}...",
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.headers.get('User-Agent') if request else None
                )

                return data
            else:
                error_msg = f"API error: {response.status_code} - {response.text}"

                # Log API error
                AuditLog.log_action(
                    user_id=current_user.id,
                    action='GENIE_API_ERROR',
                    resource='genie_api',
                    details=error_msg,
                    ip_address=request.remote_addr if request else None,
                    user_agent=request.headers.get('User-Agent') if request else None
                )

                raise Exception(error_msg)

        except requests.RequestException as e:
            error_msg = f"Network error: {str(e)}"

            # Log network error
            AuditLog.log_action(
                user_id=current_user.id,
                action='GENIE_NETWORK_ERROR',
                resource='genie_api',
                details=error_msg,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None
            )

            raise Exception(error_msg)

    def send_message(self, conversation_id, message):
        """Send a message to an existing conversation"""
        try:
            headers = self.get_auth_headers()
            url = self.get_send_message_url(conversation_id)

            response = requests.post(
                url,
                headers=headers,
                json={'content': message},
                timeout=30
            )

            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API error: {response.status_code} - {response.text}")

        except requests.RequestException as e:
            raise Exception(f"Network error: {str(e)}")

    def get_message(self, conversation_id, message_id):
        """Get a specific message from a conversation"""
        try:
            headers = self.get_auth_headers()
            url = self.get_message_url(conversation_id, message_id)

            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"API error: {response.status_code} - {response.text}")

        except requests.RequestException as e:
            raise Exception(f"Network error: {str(e)}")

    def wait_for_message(self, conversation_id, message_id, max_wait_seconds=120):
        """Wait for a message to complete and return the response"""
        import time

        start_time = time.time()

        while time.time() - start_time < max_wait_seconds:
            try:
                message_data = self.get_message(conversation_id, message_id)

                # Check if message is complete
                status = message_data.get('status')
                if status == 'COMPLETED':
                    # Extract and format the response
                    return self._format_genie_response(message_data)
                elif status == 'FAILED':
                    error_msg = message_data.get('error', {}).get('message', 'Unknown error')
                    raise Exception(f"Genie query failed: {error_msg}")

                # Wait before checking again
                time.sleep(2)

            except Exception as e:
                if "API error" in str(e):
                    # API error, wait and retry
                    time.sleep(2)
                    continue
                else:
                    # Other error, re-raise
                    raise

        # Timeout
        raise Exception(f"Timeout waiting for message completion after {max_wait_seconds} seconds")

    def _format_genie_response(self, message_data):
        """Format Genie response data for display"""
        try:
            # Extract the main content
            content = message_data.get('content', '')

            # Extract attachments (SQL queries, results, etc.)
            attachments = message_data.get('attachments', [])

            # Look for SQL query and results
            sql_query = None
            query_result = None

            for attachment in attachments:
                if attachment.get('type') == 'query':
                    query_data = attachment.get('query', {})
                    sql_query = query_data.get('query')
                elif attachment.get('type') == 'query_result':
                    query_result = attachment.get('query_result', {})

            # Format the response
            formatted_response = content

            if sql_query:
                formatted_response += f"\n\n**Generated SQL Query:**\n```sql\n{sql_query}\n```"

            if query_result:
                # Format query results
                data = query_result.get('data_array', [])
                if data:
                    formatted_response += f"\n\n**Query Results:**\n"
                    # Add table formatting here if needed
                    formatted_response += f"Found {len(data)} rows"

            return formatted_response

        except Exception as e:
            # Fallback to simple content
            return message_data.get('content', 'Response received but could not be formatted')

# Global instance
federation_api = FederationAPIClient()
