#!/usr/bin/env python3
"""
Database Fix Script for U2M OAuth Federation

This script fixes the database schema mismatch by recreating the database
with the correct U2M OAuth federation schema.
"""

import os
import sqlite3
import shutil
from datetime import datetime

def backup_and_recreate_database():
    """Backup existing database and create new one with correct schema"""
    
    db_path = "genie_federation.db"
    
    print("🔧 Fixing Database Schema for U2M OAuth Federation")
    print("=" * 60)
    
    # Step 1: Backup existing database if it exists
    if os.path.exists(db_path):
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            shutil.copy2(db_path, backup_path)
            print(f"✅ Backed up existing database to: {backup_path}")
        except Exception as e:
            print(f"⚠️  Could not backup database: {e}")
        
        # Remove old database
        os.remove(db_path)
        print(f"✅ Removed old database: {db_path}")
    else:
        print(f"ℹ️  No existing database found at: {db_path}")
    
    # Step 2: Create new database with Flask app
    print("🔨 Creating new database with U2M schema...")
    
    from flask import Flask
    from models import db, User, Organization, OrganizationConfig, ExternalIDPConfig
    
    # Create minimal Flask app
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{db_path}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'temp-key'
    
    # Initialize database
    db.init_app(app)
    
    with app.app_context():
        # Create all tables with new schema
        db.create_all()
        print("✅ Created all tables with U2M schema")
        
        # Create default organization
        default_org = Organization(
            name='Default Organization',
            domain='localhost',
            contact_email='admin@localhost',
            created_by=1  # Will be updated after user creation
        )
        db.session.add(default_org)
        db.session.flush()  # Get ID without committing
        
        # Create default admin user
        admin_user = User(
            email='<EMAIL>',
            role='admin',
            databricks_username='<EMAIL>',
            organization_id=default_org.id
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        db.session.flush()  # Get ID without committing
        
        # Update organization creator
        default_org.created_by = admin_user.id
        
        # Create default organization config
        org_config = OrganizationConfig(
            organization_id=default_org.id,
            databricks_host='your-workspace.cloud.databricks.com',
            genie_space_id='your-genie-space-id',
            audience='databricks',
            issuer_url='https://localhost:5001/oauth',
            created_by=admin_user.id
        )
        db.session.add(org_config)
        
        # Commit all changes
        db.session.commit()
        
        print("✅ Created default organization and admin user")
        print(f"   Organization: {default_org.name}")
        print(f"   Admin User: {admin_user.email}")
        print(f"   Password: admin123")
    
    print("\n🎉 Database schema fixed successfully!")
    print("\nNext steps:")
    print("1. Start the app: python app.py")
    print("2. Login with: <EMAIL> / admin123")
    print("3. Configure Databricks settings in admin panel")
    
    return True

if __name__ == "__main__":
    try:
        backup_and_recreate_database()
    except Exception as e:
        print(f"\n❌ Error fixing database: {e}")
        print("Please check the error and try again.")
