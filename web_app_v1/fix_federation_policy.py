#!/usr/bin/env python3
"""
Fix Federation Policy Creation

This script helps you create the federation policy with the correct token and endpoint.
"""

import requests
import json

def test_account_access(account_id, token):
    """Test if the token has account-level access"""
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print(f"🔍 Testing account access for: {account_id}")
    print("-" * 60)
    
    # Test account info endpoint
    try:
        response = requests.get(
            f"https://accounts.cloud.databricks.com/api/2.0/accounts/{account_id}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            account_info = response.json()
            print(f"✅ Account access confirmed")
            print(f"   Account Name: {account_info.get('account_name', 'Unknown')}")
            print(f"   Account ID: {account_info.get('account_id', 'Unknown')}")
            return True
        elif response.status_code == 401:
            print(f"❌ Unauthorized - Token doesn't have account access")
            print(f"   Error: {response.text}")
            return False
        elif response.status_code == 403:
            print(f"🔒 Forbidden - Token has access but insufficient permissions")
            print(f"   Error: {response.text}")
            return False
        else:
            print(f"⚠️  Unexpected response: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing account access: {e}")
        return False

def create_federation_policy(account_id, token, issuer, audience, subject_claim):
    """Create federation policy with account-level token"""
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    policy_data = {
        "oidc_policy": {
            "issuer": issuer,
            "audiences": [audience],
            "subject_claim": subject_claim
        }
    }
    
    print(f"🔧 Creating federation policy...")
    print("-" * 60)
    print(f"Policy data: {json.dumps(policy_data, indent=2)}")
    print()
    
    try:
        response = requests.post(
            f"https://accounts.cloud.databricks.com/api/2.0/accounts/{account_id}/federationPolicies",
            headers=headers,
            json=policy_data,
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            policy_info = response.json()
            print(f"✅ Federation policy created successfully!")
            print(f"Policy ID: {policy_info.get('policy_id')}")
            print(f"Response: {json.dumps(policy_info, indent=2)}")
            return policy_info
        else:
            print(f"❌ Failed to create federation policy")
            print(f"Status: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error creating federation policy: {e}")
        return None

def get_account_token_instructions():
    """Provide instructions for getting account-level token"""
    
    print("📋 HOW TO GET ACCOUNT-LEVEL TOKEN")
    print("=" * 60)
    print()
    print("Your current token is workspace-level. You need an account-level token.")
    print()
    print("🔧 Method 1: Databricks CLI")
    print("-" * 30)
    print("1. Install/update Databricks CLI:")
    print("   pip install --upgrade databricks-cli")
    print()
    print("2. Configure for account-level access:")
    print("   databricks configure --host https://accounts.cloud.databricks.com")
    print("   - Enter your account-level credentials")
    print()
    print("3. Get account token:")
    print("   databricks auth token")
    print()
    print("🔧 Method 2: Account Console")
    print("-" * 30)
    print("1. Go to: https://accounts.cloud.databricks.com/")
    print("2. Navigate to: Settings → Developer → Access tokens")
    print("3. Generate new token with account-level permissions")
    print()
    print("🔧 Method 3: Service Principal")
    print("-" * 30)
    print("1. Create account-level service principal")
    print("2. Generate OAuth secret for the service principal")
    print("3. Use service principal credentials")
    print()

def main():
    """Main function"""
    
    print("🔧 Federation Policy Creation Fix")
    print("=" * 60)
    
    # Configuration
    account_id = "bdd3103b-f558-4e9d-b5f6-42152a7aec03"
    issuer = "https://trial-8111244.okta.com/oauth2/default"
    audience = "databricks"
    subject_claim = "sub"
    
    print(f"📋 Configuration:")
    print(f"   Account ID: {account_id}")
    print(f"   Issuer: {issuer}")
    print(f"   Audience: {audience}")
    print(f"   Subject Claim: {subject_claim}")
    print()
    
    # Get token
    print("Please provide your Databricks token:")
    print("(If you have a workspace token, we'll test it and provide instructions for account token)")
    token = input("Token: ").strip()
    
    if not token:
        print("❌ Token is required")
        return
    
    # Test account access
    has_account_access = test_account_access(account_id, token)
    
    if has_account_access:
        # Try to create federation policy
        policy_info = create_federation_policy(account_id, token, issuer, audience, subject_claim)
        
        if policy_info:
            print("\n🎉 SUCCESS!")
            print("=" * 60)
            print("Federation policy created successfully!")
            print(f"Policy ID: {policy_info.get('policy_id')}")
            print()
            print("You can now test OAuth federation in your application.")
        else:
            print("\n⚠️  Federation policy creation failed.")
            print("Check the error messages above for details.")
    else:
        print("\n📋 SOLUTION REQUIRED")
        print("=" * 60)
        get_account_token_instructions()
        
        print("\n🔄 Alternative: Use Workspace-Level Configuration")
        print("-" * 50)
        print("If account-level access is not available, you can:")
        print("1. Use workspace-level authentication (PAT tokens)")
        print("2. Configure the app to skip federation policy creation")
        print("3. Use service principal authentication directly")
        print()
        print("For immediate testing, use your workspace PAT token in the app configuration.")

if __name__ == "__main__":
    main()
