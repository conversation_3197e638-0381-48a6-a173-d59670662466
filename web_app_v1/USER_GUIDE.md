# OAuth Federation Web App - Complete User Guide

## 🎯 Overview
This guide explains how to configure and use the OAuth Federation web application for Databricks Genie.

## 📋 Step-by-Step Configuration

### Step 1: Initial Login
- **URL**: http://localhost:5003 (or your deployed URL)
- **Default Admin Credentials**:
  - Email: `<EMAIL>`
  - Password: `admin123`

### Step 2: Find Your Genie Space ID

1. **Go to Databricks Workspace**
2. **Navigate to Genie**: Click on "Genie" in the left sidebar
3. **Open Your Genie Space**: Click on the space you want to use
4. **Copy Space ID from URL**: 
   ```
   https://your-workspace.cloud.databricks.com/genie/spaces/12ab345cd6789000ef6a2fb844ba2d3f
                                                    ↑
                                            This is your Space ID
   ```

### Step 3: Configuration Form Fields

#### 🏢 Organization Settings
- **Databricks Host**: `dbc-620d1468-0f52.cloud.databricks.com`
- **Genie Space ID**: `12ab345cd6789000ef6a2fb844ba2d3f` (from Step 2)
- **Databricks Account ID**: Leave empty (for workspace-level auth)

#### 🔐 Identity Provider Configuration
- **Identity Provider URL**: 
  - Local: `http://localhost:5003/oauth`
  - Production: `https://databricks-accelerator.tudip.ai/oauth`
- **Token Audience**: `databricks`
- **Subject Claim**: `sub`

#### 🔒 Security Configuration
- **✅ Check**: "Generate New RSA Key Pair"

### Step 4: Save Configuration
Click "Save Configuration" button.

## 🔧 After Configuration - What Happens Next?

### 1. Federation Setup Guide
After saving configuration, you'll see a "Federation Setup Guide" with:
- **JWKS URL**: Copy this URL
- **OpenID Configuration**: Copy this URL
- **Federation Policy JSON**: Copy this JSON

### 2. Configure Databricks Federation Policy

You need to create a federation policy in Databricks using the CLI:

```bash
# Install Databricks CLI if not installed
pip install databricks-cli

# Configure CLI with your credentials
databricks configure --token

# Create federation policy
databricks account federation-policies create --json '{
  "oidc_policy": {
    "issuer": "http://localhost:5003/oauth",
    "audiences": ["databricks"],
    "subject_claim": "sub"
  }
}'
```

### 3. Create Users
Go to Admin > Users to create user accounts:
- **Email**: User's email address
- **Password**: Temporary password
- **Role**: "user" (for regular users) or "admin" (for administrators)
- **Databricks Username**: Usually same as email

## 🎮 Using the Application

### For Admin Users:
1. **Dashboard**: View system status and recent activity
2. **Configuration**: Modify organization settings
3. **Users**: Create and manage user accounts
4. **Federation Setup**: View setup instructions
5. **Audit Logs**: Monitor user activities

### For Regular Users:
1. **Login**: Use email/password provided by admin
2. **Conversation**: Ask questions to Genie
3. **Profile**: View account information

## 🔍 Testing the System

### 1. Test User Login
- Create a test user account
- Login with the new credentials
- Verify you can access the conversation page

### 2. Test Genie Queries
- Go to the conversation page
- Ask a question like: "Show me the top 10 customers by revenue"
- Verify the response comes back

### 3. Verify User Identity in Databricks
- Go to Databricks workspace
- Check audit logs or query history
- Verify queries show the actual user email, not a service principal

## 🚨 Troubleshooting

### Common Issues:

1. **"Organization not configured"**
   - Solution: Complete the configuration form as admin

2. **"Federation policy not found"**
   - Solution: Create the federation policy in Databricks using CLI

3. **"Token exchange failed"**
   - Solution: Verify Databricks host and federation policy are correct

4. **"Permission denied"**
   - Solution: Ensure user has access to the Genie space in Databricks

### Debug Steps:
1. Check Admin > Audit Logs for detailed error messages
2. Verify all configuration fields are correct
3. Test network connectivity to Databricks
4. Ensure federation policy is active in Databricks

## 🔐 Security Notes

- **Change default admin password** immediately
- **Use HTTPS** in production
- **Secure database** with proper credentials
- **Monitor audit logs** regularly

## 📞 Support

If you encounter issues:
1. Check the audit logs in Admin panel
2. Verify configuration settings
3. Test individual components (login, token creation, API calls)
4. Contact Databricks support for federation policy issues
