#!/bin/bash

# Databricks OAuth Federation Policy Creation Script
# This script creates the federation policy using curl and the REST API

echo "🔐 Creating Databricks OAuth Federation Policy"
echo "=============================================="

# Configuration
ACCOUNT_HOST="https://accounts.cloud.databricks.com"
USERNAME="<EMAIL>"

# Prompt for password
echo -n "Enter your Databricks password: "
read -s PASSWORD
echo

# Create federation policy using curl
echo "🔨 Creating federation policy..."

curl -X POST "${ACCOUNT_HOST}/api/2.0/accounts/federation-policies" \
  -H "Content-Type: application/json" \
  -u "${USERNAME}:${PASSWORD}" \
  -d @federation-policy.json \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo
echo "✅ Federation policy creation request sent"
echo
echo "📋 Next Steps:"
echo "1. Check if the policy was created successfully"
echo "2. Note the policy ID from the response"
echo "3. Update your web app configuration"
echo "4. Test the U2M authentication flow"

# List existing policies
echo
echo "📋 Listing existing federation policies..."
curl -X GET "${ACCOUNT_HOST}/api/2.0/accounts/federation-policies" \
  -H "Content-Type: application/json" \
  -u "${USERNAME}:${PASSWORD}" \
  -s | python3 -m json.tool

echo
echo "🎉 Federation policy setup complete!"
