#!/usr/bin/env python3
"""
Demo Federation Setup for Manager Presentation

This script sets up a mock federation configuration that demonstrates
the U2M authentication concept without requiring actual Databricks
federation policy creation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, OrganizationConfig, User
from auth_federation import jwt_provider
from flask import Flask

def setup_demo_federation():
    """Setup demo federation configuration for presentation"""
    
    print("🎯 Setting up Demo Federation for Manager Presentation")
    print("=" * 60)
    
    # Create Flask app context
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///genie_federation.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'demo-secret-key'
    
    db.init_app(app)
    
    with app.app_context():
        # Check if configuration already exists
        existing_config = OrganizationConfig.query.first()
        if existing_config:
            print("📋 Existing configuration found. Updating...")
            config = existing_config
        else:
            print("📋 Creating new demo configuration...")
            admin_user = User.query.filter_by(role='admin').first()
            if not admin_user:
                print("❌ Admin user not found. Please run the app first to create admin user.")
                return
            
            config = OrganizationConfig(
                databricks_host='demo-workspace.cloud.databricks.com',
                genie_space_id='demo-genie-space-123',
                issuer_url='http://localhost:5001/oauth',
                audience='databricks',
                subject_claim='sub',
                created_by=admin_user.id
            )
        
        # Generate RSA keys for JWT signing
        print("🔐 Generating RSA key pair for JWT signing...")
        private_key, public_key, key_id = jwt_provider.generate_rsa_keypair()
        
        # Update configuration
        config.databricks_host = 'demo-workspace.cloud.databricks.com'
        config.genie_space_id = 'demo-genie-space-123'
        config.issuer_url = 'http://localhost:5001/oauth'
        config.audience = 'databricks'
        config.subject_claim = 'sub'
        config.private_key_pem = private_key
        config.public_key_pem = public_key
        config.key_id = key_id
        config.federation_policy_configured = True
        config.federation_policy_id = 'demo-policy-12345'
        
        if not existing_config:
            db.session.add(config)
        
        db.session.commit()
        
        print("✅ Demo federation configuration created successfully!")
        print()
        print("📋 Configuration Details:")
        print(f"   🏢 Databricks Host: {config.databricks_host}")
        print(f"   🎯 Genie Space ID: {config.genie_space_id}")
        print(f"   🔗 Issuer URL: {config.issuer_url}")
        print(f"   👥 Audience: {config.audience}")
        print(f"   🆔 Subject Claim: {config.subject_claim}")
        print(f"   🔑 Key ID: {config.key_id}")
        print(f"   📜 Federation Policy ID: {config.federation_policy_id}")
        print()
        print("🎯 Demo Features Enabled:")
        print("   ✅ JWT Token Generation")
        print("   ✅ Individual User Identity Mapping")
        print("   ✅ Multi-tenant Organization Support")
        print("   ✅ OAuth Federation Simulation")
        print("   ✅ U2M Authentication Flow")
        print()
        print("🚀 Ready for Manager Presentation!")
        print("   📍 Access: http://localhost:5001")
        print("   🔐 Admin: <EMAIL> / admin123")
        print("   📊 Demo: Shows U2M vs M2M benefits")

def test_jwt_generation():
    """Test JWT token generation with demo config"""
    
    print("\n🧪 Testing JWT Token Generation")
    print("-" * 40)
    
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///genie_federation.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # Get admin user
        admin_user = User.query.filter_by(role='admin').first()
        if not admin_user:
            print("❌ Admin user not found")
            return
        
        try:
            # Generate JWT token
            token, expires_at = jwt_provider.create_jwt_token(
                user=admin_user,
                session_id='demo-session-123'
            )
            
            print("✅ JWT Token generated successfully!")
            print(f"   👤 User: {admin_user.email}")
            print(f"   🎫 Token: {token[:50]}...")
            print(f"   ⏰ Expires: {expires_at}")
            print(f"   🔍 Subject: {admin_user.databricks_username or admin_user.email}")
            
            # Verify token
            payload = jwt_provider.verify_jwt_token(token)
            print("✅ JWT Token verification successful!")
            print(f"   📋 Payload: {payload}")
            
        except Exception as e:
            print(f"❌ JWT Token generation failed: {e}")

def main():
    """Main setup function"""
    print("🎯 Demo Federation Setup for U2M Authentication")
    print("=" * 60)
    print()
    print("This script sets up a complete demo environment that showcases:")
    print("• Individual user identity preservation (U2M)")
    print("• JWT token generation and verification")
    print("• Multi-tenant organization support")
    print("• OAuth federation simulation")
    print("• Complete audit trail with user tracking")
    print()
    
    try:
        setup_demo_federation()
        test_jwt_generation()
        
        print("\n" + "=" * 60)
        print("🎉 Demo Federation Setup Complete!")
        print()
        print("📋 Next Steps for Manager Presentation:")
        print("1. Start the web app: python app.py")
        print("2. Login as admin: <EMAIL> / admin123")
        print("3. Show configuration: Admin → Configuration")
        print("4. Demonstrate U2M benefits vs M2M")
        print("5. Show individual user identity preservation")
        print()
        print("🎯 Key Demo Points:")
        print("• Each user gets individual JWT tokens")
        print("• Databricks sees real user identity, not service account")
        print("• Full compliance and audit trail")
        print("• Multi-tenant enterprise ready")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
