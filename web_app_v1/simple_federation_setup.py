#!/usr/bin/env python3
"""
Simple Databricks Federation Policy Setup

This script provides multiple approaches to create the OAuth federation policy
for U2M authentication with clear instructions and troubleshooting.
"""

import json
import subprocess
import sys

def print_header():
    print("🔐 Databricks OAuth Federation Policy Setup")
    print("=" * 60)
    print()

def print_section(title):
    print(f"\n📋 {title}")
    print("-" * 50)

def approach_1_databricks_cli():
    print_section("Approach 1: Using Databricks CLI (Recommended)")
    
    print("1️⃣ Install/Upgrade Databricks CLI:")
    print("   pip install databricks-cli --upgrade")
    print()
    
    print("2️⃣ Configure CLI with Account Admin:")
    print("   databricks configure --host https://accounts.cloud.databricks.com")
    print("   📧 Username: Your Databricks account admin email")
    print("   🔑 Password: Your Databricks account password")
    print()
    
    print("3️⃣ Create Federation Policy:")
    print("   databricks account federation-policies create \\")
    print("     --json-file federation-policy.json")
    print()
    
    print("📄 federation-policy.json content:")
    policy = {
        "name": "U2M OAuth Federation Policy",
        "description": "OAuth federation policy for User-to-Machine authentication preserving individual user identity",
        "oidc_policy": {
            "issuer": "https://localhost:5001/oauth",
            "audiences": ["databricks"],
            "subject_claim": "sub",
            "email_claim": "email",
            "groups_claim": "groups"
        }
    }
    print(json.dumps(policy, indent=2))

def approach_2_rest_api():
    print_section("Approach 2: Using REST API with curl")
    
    print("1️⃣ Get your Account ID:")
    print("   - Go to https://accounts.cloud.databricks.com")
    print("   - Look in the URL or account settings")
    print("   - Format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx")
    print()
    
    print("2️⃣ Create Personal Access Token:")
    print("   - Go to your workspace (dbc-xxxxx.cloud.databricks.com)")
    print("   - Settings → Developer → Access tokens")
    print("   - Generate new token")
    print()
    
    print("3️⃣ Create Federation Policy with curl:")
    print("""
curl --request POST \\
  --header "Authorization: Bearer YOUR_PAT_TOKEN" \\
  --header "Content-Type: application/json" \\
  "https://accounts.cloud.databricks.com/api/2.0/accounts/YOUR_ACCOUNT_ID/federation-policies" \\
  --data '{
    "name": "U2M OAuth Federation Policy",
    "description": "OAuth federation policy for User-to-Machine authentication",
    "oidc_policy": {
      "issuer": "https://localhost:5001/oauth",
      "audiences": ["databricks"],
      "subject_claim": "sub",
      "email_claim": "email"
    }
  }'
""")

def approach_3_web_interface():
    print_section("Approach 3: Using Databricks Web Interface")
    
    print("1️⃣ Go to Account Console:")
    print("   https://accounts.cloud.databricks.com")
    print()
    
    print("2️⃣ Navigate to Identity & Access:")
    print("   - Click on 'Identity & Access' in left sidebar")
    print("   - Go to 'Federation' section")
    print()
    
    print("3️⃣ Create Federation Policy:")
    print("   - Click 'Add Federation Policy'")
    print("   - Name: U2M OAuth Federation Policy")
    print("   - Issuer: https://localhost:5001/oauth")
    print("   - Audience: databricks")
    print("   - Subject Claim: sub")
    print("   - Email Claim: email")

def troubleshooting():
    print_section("🔍 Troubleshooting")
    
    print("❌ 'Basic Authentication is disabled':")
    print("   → Use Personal Access Token instead of username/password")
    print("   → Create PAT in workspace, use for account-level API calls")
    print()
    
    print("❌ 'Endpoint not found':")
    print("   → Use https://accounts.cloud.databricks.com (not workspace URL)")
    print("   → Check if you have account admin permissions")
    print()
    
    print("❌ 'Permission denied':")
    print("   → Ensure you have account admin role")
    print("   → Contact your Databricks account administrator")
    print()
    
    print("❌ 'CLI command not found':")
    print("   → Install: pip install databricks-cli")
    print("   → Upgrade: pip install databricks-cli --upgrade")

def why_federation():
    print_section("🎯 Why OAuth Federation?")
    
    print("🔄 Without Federation (M2M):")
    print("   - Service principal authenticates")
    print("   - All queries appear as one user")
    print("   - No individual user tracking")
    print("   - Compliance issues")
    print()
    
    print("✅ With Federation (U2M):")
    print("   - Individual users authenticate via SSO")
    print("   - Each query shows actual user identity")
    print("   - Full audit trail per user")
    print("   - Compliance ready")
    print("   - Better security and governance")

def next_steps():
    print_section("🚀 Next Steps After Creating Federation Policy")
    
    print("1️⃣ Note the Policy ID from the response")
    print("2️⃣ Update your web app configuration:")
    print("   - Go to http://localhost:5001/admin/config")
    print("   - Enter the federation policy ID")
    print("3️⃣ Configure external identity provider (Google/Azure)")
    print("4️⃣ Test the complete U2M authentication flow")
    print("5️⃣ Verify user identity in Databricks audit logs")

def main():
    print_header()
    
    print("This script provides multiple approaches to create the Databricks")
    print("OAuth federation policy for U2M authentication.")
    print()
    
    approach_1_databricks_cli()
    approach_2_rest_api()
    approach_3_web_interface()
    troubleshooting()
    why_federation()
    next_steps()
    
    print("\n" + "=" * 60)
    print("🎉 Choose the approach that works best for your environment!")
    print("💡 If one approach fails, try another one.")
    print("📞 Contact your Databricks admin if you need account permissions.")

if __name__ == "__main__":
    main()
