# 🔐 OAuth Federation Setup Guide for U2M Authentication

This guide explains how to set up OAuth federation for User-to-Machine (U2M) authentication with Databricks, preserving individual user identity in audit logs.

## 📋 **Overview**

### What is U2M vs M2M?
- **M2M (Machine-to-Machine)**: Service principal authenticates, all queries appear as one user
- **U2M (User-to-Machine)**: Individual users authenticate, queries preserve user identity

### Architecture
```
User → External IDP (Google/Azure) → Your App → JWT Token → Databricks → Individual User Identity
```

## 🏗️ **Step 1: Databricks Federation Policy Setup**

### Prerequisites
1. **Databricks Account Admin** access
2. **Personal Access Token** from your workspace
3. **Account URL**: `https://accounts.cloud.databricks.com`

### Create Personal Access Token
1. Go to your **Databricks workspace** (e.g., `https://dbc-xxxxx.cloud.databricks.com`)
2. Click **profile picture** → **Settings**
3. Navigate to **Developer** → **Access tokens**
4. Click **Generate new token**
5. Name: `U2M Federation Setup`
6. Lifetime: `90 days` (or leave blank)
7. Click **Generate** and **copy immediately**

### Run Federation Setup Script
```bash
python setup_databricks_federation.py
```

**Configuration Values:**
- **Account Host**: `https://accounts.cloud.databricks.com` (NOT your workspace URL)
- **Authentication**: Choose option 1 (Personal Access Token)
- **Issuer URL**: `https://localhost:5001/oauth` (your app's OAuth endpoint)
- **Audience**: `databricks`
- **Subject Claim**: `sub`

## 🔧 **Step 2: Web App Configuration**

### Admin Panel Configuration
1. **Login**: `http://localhost:5001`
2. **Credentials**: `<EMAIL> / admin123`
3. **Go to**: Admin → Configuration

### Required Fields

#### Databricks Settings
- **Databricks Host**: Your workspace URL (e.g., `dbc-xxxxx.cloud.databricks.com`)
- **Genie Space ID**: Your Genie space identifier
- **Account ID**: Your Databricks account ID (optional)

#### OAuth Federation Settings
- **Issuer URL**: `https://localhost:5001/oauth` (your identity provider)
- **Audience**: `databricks` (who the token is for)
- **Subject Claim**: `sub` (user identifier claim)

#### RSA Key Generation
- Click **Generate New RSA Keys** to create signing keys
- Keys are used to sign JWT tokens for federation

## 🌐 **Step 3: External Identity Provider Setup**

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create/select project
3. Enable **Google+ API**
4. Create **OAuth 2.0 Client ID**
5. Add redirect URI: `https://localhost:5001/auth/external/google/callback`

### Azure AD Setup
1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** → **App registrations**
3. Click **New registration**
4. Add redirect URI: `https://localhost:5001/auth/external/azure/callback`
5. Note **Application (client) ID** and **Directory (tenant) ID**

### Configuration in Web App
1. Go to **Admin** → **External IDP Configuration**
2. Add your OAuth credentials:
   - **Client ID**: From Google/Azure
   - **Client Secret**: From Google/Azure
   - **Tenant ID**: (Azure only)

## 🔄 **Step 4: Testing the U2M Flow**

### Test Authentication
1. **Logout** from admin account
2. **Login** using external IDP (Google/Azure)
3. **Verify** user identity is preserved

### Test Conversation
1. Go to **Conversation** page
2. Ask a question: `"Show me recent sales data"`
3. **Verify** the response shows your individual user identity

### Verify Audit Logs
1. **Admin** → **Audit Logs**
2. **Check** that actions show individual users, not service principal
3. **Confirm** Databricks audit logs show actual user names

## 🎯 **Key Benefits for Manager Presentation**

### Individual User Identity
- ✅ **Audit Trail**: Each query shows actual user, not service account
- ✅ **Compliance**: Meets regulatory requirements for user tracking
- ✅ **Security**: Fine-grained access control per user

### Scalable Architecture
- ✅ **Multi-tenant**: Support multiple organizations/clients
- ✅ **SSO Integration**: Works with corporate identity providers
- ✅ **Zero Trust**: No shared service accounts

### Operational Benefits
- ✅ **Admin Control**: Centralized configuration management
- ✅ **User Experience**: Seamless SSO login
- ✅ **Monitoring**: Comprehensive audit logging

## 🔍 **Troubleshooting**

### Common Issues

#### "Basic Authentication Disabled"
- **Solution**: Use Personal Access Token instead of username/password
- **Create PAT**: In workspace, not account console

#### "Endpoint Not Found"
- **Solution**: Use `https://accounts.cloud.databricks.com`, not workspace URL
- **Check**: Account vs workspace URL confusion

#### "Permission Denied"
- **Solution**: Ensure account admin permissions
- **Verify**: PAT has account-level access

#### "Federation Policy Creation Failed"
- **Solution**: Check account admin role
- **Verify**: Correct issuer URL format

### Testing Checklist
- [ ] PAT token created in workspace
- [ ] Account URL used (not workspace)
- [ ] Federation policy created successfully
- [ ] Web app configuration complete
- [ ] External IDP configured
- [ ] User can login via SSO
- [ ] Conversation works
- [ ] Audit logs show individual users

## 📞 **Support**

### Log Files
- **Web App Logs**: Check terminal output
- **Audit Logs**: Admin → Audit Logs
- **Browser Console**: F12 → Console tab

### Configuration Files
- **Federation Config**: `databricks_federation_config.json`
- **Web App Config**: Admin panel settings
- **Database**: SQLite file with user sessions

### Next Steps
1. **Production Deployment**: Use HTTPS and proper certificates
2. **External IDP**: Configure corporate SSO
3. **Monitoring**: Set up comprehensive logging
4. **Scaling**: Add load balancing and redundancy

---

## 🎉 **Success Criteria**

Your U2M OAuth federation is working when:
- ✅ Users login via corporate SSO
- ✅ Individual user identity preserved in Databricks
- ✅ Audit logs show actual users, not service accounts
- ✅ Admin can manage multiple organizations
- ✅ Compliance requirements met
