#!/usr/bin/env python3
"""
Okta Authentication Routes for U2M OAuth Federation

This module implements Okta OAuth authentication routes that integrate
with the U2M (User-to-Machine) authentication system.
"""

import secrets
from flask import Blueprint, request, redirect, url_for, flash, session, current_app
from flask_login import login_user, current_user
from models import db, User, ExternalIDPConfig, UserSession, AuditLog, OrganizationConfig
from auth_federation import external_idp_provider, jwt_provider

# Create blueprint
okta_bp = Blueprint('okta_auth', __name__, url_prefix='/auth/external/okta')

@okta_bp.route('/login')
def login():
    """Initiate Okta OAuth login"""
    try:
        # Get Okta configuration
        config = OrganizationConfig.get_current_config()
        if not config or not config.organization_id:
            flash('System not configured. Please contact administrator.', 'danger')
            return redirect(url_for('auth.login'))
        
        okta_config = ExternalIDPConfig.query.filter_by(
            organization_id=config.organization_id,
            provider_name='okta',
            is_active=True
        ).first()
        
        if not okta_config:
            flash('Okta authentication not configured. Please contact administrator.', 'danger')
            return redirect(url_for('auth.login'))
        
        # Generate state parameter for security
        state = secrets.token_urlsafe(32)
        session['oauth_state'] = state
        session['oauth_provider'] = 'okta'
        
        # Generate redirect URI
        redirect_uri = url_for('okta_auth.callback', _external=True)
        
        # Get authorization URL from external IDP provider
        auth_url, code_verifier = external_idp_provider.get_authorization_url(
            provider='okta',
            client_id=okta_config.client_id,
            redirect_uri=redirect_uri,
            state=state,
            domain=okta_config.domain
        )
        
        # Store code verifier for PKCE
        session['code_verifier'] = code_verifier
        
        # Log authentication attempt
        AuditLog.log_action(
            user_id=current_user.id if current_user.is_authenticated else None,
            action='OKTA_AUTH_INITIATED',
            resource='external_auth',
            details=f"Okta authentication initiated for domain: {okta_config.domain}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        return redirect(auth_url)
        
    except Exception as e:
        current_app.logger.error(f"Okta login error: {str(e)}")
        flash('Authentication error. Please try again.', 'danger')
        return redirect(url_for('auth.login'))

@okta_bp.route('/callback')
def callback():
    """Handle Okta OAuth callback"""
    try:
        # Verify state parameter
        if 'oauth_state' not in session or request.args.get('state') != session['oauth_state']:
            flash('Invalid authentication state. Please try again.', 'danger')
            return redirect(url_for('auth.login'))
        
        # Check for error in callback
        if 'error' in request.args:
            error_description = request.args.get('error_description', 'Unknown error')
            flash(f'Authentication failed: {error_description}', 'danger')
            return redirect(url_for('auth.login'))
        
        # Get authorization code
        code = request.args.get('code')
        if not code:
            flash('No authorization code received.', 'danger')
            return redirect(url_for('auth.login'))
        
        # Get Okta configuration
        config = OrganizationConfig.get_current_config()
        okta_config = ExternalIDPConfig.query.filter_by(
            organization_id=config.organization_id,
            provider_name='okta',
            is_active=True
        ).first()
        
        if not okta_config:
            flash('Okta configuration not found.', 'danger')
            return redirect(url_for('auth.login'))
        
        # Exchange code for token
        redirect_uri = url_for('okta_auth.callback', _external=True)
        code_verifier = session.get('code_verifier')
        
        token_data = external_idp_provider.exchange_code_for_token(
            provider='okta',
            client_id=okta_config.client_id,
            client_secret=okta_config.client_secret,
            code=code,
            redirect_uri=redirect_uri,
            code_verifier=code_verifier,
            domain=okta_config.domain
        )
        
        # Get user information from Okta
        user_info = external_idp_provider.get_user_info(
            provider='okta',
            access_token=token_data['access_token'],
            domain=okta_config.domain
        )
        
        # Find or create user
        user = User.query.filter_by(email=user_info['email']).first()
        
        if not user:
            # Auto-create user if enabled
            if okta_config.auto_create_users:
                user = User(
                    email=user_info['email'],
                    role=okta_config.default_role,
                    databricks_username=user_info['email'],  # Use email as Databricks username
                    is_active=True
                )
                # Set a random password (user won't use it)
                user.set_password(secrets.token_urlsafe(32))
                db.session.add(user)
                db.session.commit()
                
                # Log user creation
                AuditLog.log_action(
                    user_id=user.id,
                    action='USER_AUTO_CREATED',
                    resource='okta_auth',
                    details=f"User auto-created from Okta: {user.email}",
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
            else:
                flash('User not found and auto-creation is disabled. Please contact administrator.', 'danger')
                return redirect(url_for('auth.login'))
        
        if not user.is_active:
            flash('Your account is deactivated. Please contact administrator.', 'danger')
            return redirect(url_for('auth.login'))
        
        # Create user session
        user_session = UserSession.create_session(
            user_id=user.id,
            user_agent=request.headers.get('User-Agent'),
            ip_address=request.remote_addr
        )
        
        # Generate JWT token for U2M authentication
        jwt_token, jwt_expires_at = jwt_provider.create_jwt_token(
            user=user,
            session_id=user_session.session_token,
            external_user_info=user_info
        )
        
        # Store JWT token in session
        user_session.jwt_token = jwt_token
        user_session.jwt_expires_at = jwt_expires_at
        db.session.commit()
        
        # Login user
        login_user(user, remember=True)
        
        # Store session token in Flask session
        session['user_session_token'] = user_session.session_token
        
        # Clean up OAuth session data
        session.pop('oauth_state', None)
        session.pop('oauth_provider', None)
        session.pop('code_verifier', None)
        
        # Log successful authentication
        AuditLog.log_action(
            user_id=user.id,
            action='OKTA_AUTH_SUCCESS',
            resource='external_auth',
            details=f"Successful Okta authentication for: {user.email}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        flash(f'Welcome, {user_info.get("name", user.email)}!', 'success')
        
        # Redirect to intended page or dashboard
        next_page = session.pop('next_page', None)
        if user.role == 'admin':
            return redirect(next_page or url_for('admin.dashboard'))
        else:
            return redirect(next_page or url_for('main.conversation'))
        
    except Exception as e:
        current_app.logger.error(f"Okta callback error: {str(e)}")
        
        # Log failed authentication
        AuditLog.log_action(
            user_id=current_user.id if current_user.is_authenticated else None,
            action='OKTA_AUTH_FAILED',
            resource='external_auth',
            details=f"Okta authentication failed: {str(e)}",
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        flash('Authentication failed. Please try again.', 'danger')
        return redirect(url_for('auth.login'))

@okta_bp.route('/test')
def test_config():
    """Test Okta configuration (admin only)"""
    from flask_login import login_required
    from admin_routes import admin_required
    
    @login_required
    @admin_required
    def _test():
        try:
            config = OrganizationConfig.get_current_config()
            if not config:
                return {'error': 'No configuration found'}, 400
            
            okta_config = ExternalIDPConfig.query.filter_by(
                organization_id=config.organization_id,
                provider_name='okta',
                is_active=True
            ).first()
            
            if not okta_config:
                return {'error': 'Okta not configured'}, 400
            
            # Test configuration
            test_result = {
                'domain': okta_config.domain,
                'client_id': okta_config.client_id[:8] + '...',
                'configured': True,
                'redirect_uri': url_for('okta_auth.callback', _external=True)
            }
            
            return test_result
            
        except Exception as e:
            return {'error': str(e)}, 500
    
    return _test()
