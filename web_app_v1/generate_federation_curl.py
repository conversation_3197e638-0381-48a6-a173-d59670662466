#!/usr/bin/env python3
"""
Generate Curl Commands for Databricks Federation Policy Setup

This script generates the correct curl commands for creating federation policies
using OAuth credentials instead of PAT tokens.
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Flask app and models
from app import app
from models import OrganizationConfig

def generate_oauth_commands():
    """Generate OAuth-based curl commands for federation setup"""

    with app.app_context():
        config = OrganizationConfig.get_current_config()
        if not config:
            print("❌ No organization configuration found. Please configure the system first.")
            return

        print("🔐 Databricks Federation Policy Setup - OAuth Method")
        print("=" * 60)
        print()

        # Determine endpoints based on account vs workspace level
        if config.databricks_account_id:
            auth_endpoint = f"https://accounts.cloud.databricks.com/oidc/accounts/{config.databricks_account_id}/v1/authorize"
            token_endpoint = f"https://accounts.cloud.databricks.com/oidc/accounts/{config.databricks_account_id}/v1/token"
            policy_endpoint = f"https://accounts.cloud.databricks.com/api/2.0/accounts/{config.databricks_account_id}/federation-policies"
            oauth_setup_url = "https://accounts.cloud.databricks.com/"
            oauth_path = "Settings → App connections"
        else:
            auth_endpoint = f"https://{config.databricks_host}/oidc/v1/authorize"
            token_endpoint = f"https://{config.databricks_host}/oidc/v1/token"
            # Workspace-level federation policies (different endpoint)
            policy_endpoint = f"https://{config.databricks_host}/api/2.0/workspace-conf"
            oauth_setup_url = f"https://{config.databricks_host}/"
            oauth_path = "Settings → Developer → OAuth"

        print("📋 Current Configuration:")
        print(f"   Databricks Host: {config.databricks_host}")
        if config.databricks_account_id:
            print(f"   Account ID: {config.databricks_account_id}")
        print(f"   Issuer URL: {config.get_issuer_url()}")
        print(f"   Audience: {config.audience}")
        print(f"   Subject Claim: {config.subject_claim}")
        print()

        print("🔧 Step 1: Create OAuth Application in Databricks")
        print("-" * 50)
        print("⚠️  IMPORTANT: OAuth app creation location depends on your setup:")
        print()
        if config.databricks_account_id:
            print("📍 ACCOUNT-LEVEL (You have account ID):")
            print("   1. Go to: https://accounts.cloud.databricks.com/")
            print("   2. Navigate to: Settings → Identity and access → Service principals")
            print("   3. Click 'Add service principal'")
            print("   4. Create service principal, then go to OAuth tab")
        else:
            print("📍 WORKSPACE-LEVEL (No account ID):")
            print(f"   1. Go to: {oauth_setup_url}")
            print("   2. Navigate to: Settings → Admin Console → Access Control")
            print("   3. Look for 'Service Principals' or 'OAuth' section")
            print("   4. If not available, try: Settings → Workspace Settings → Advanced")
        print()
        print("🔍 ALTERNATIVE METHODS:")
        print("   Method A: Use Databricks CLI")
        print("   Method B: Use REST API directly")
        print("   Method C: Contact Databricks support for OAuth app creation")
        print()
        print("📝 OAuth Application Settings (when you find the right place):")
        print("   - Name: Genie U2M Federation")
        print("   - Redirect URI: http://localhost:8080/callback")
        print("   - Scopes: all-apis")
        print("   - Grant Types: authorization_code, refresh_token")
        print()

        print("🔧 Step 2: Get Authorization Code")
        print("-" * 50)
        print("Replace YOUR_CLIENT_ID with your actual client ID and open this URL in browser:")
        print()
        auth_url = f"{auth_endpoint}?client_id=YOUR_CLIENT_ID&response_type=code&redirect_uri=http://localhost:8080/callback&scope=all-apis&state=federation_setup"
        print(f"{auth_url}")
        print()
        print("After authorization, copy the 'code' parameter from the callback URL")
        print()

        print("🔧 Step 3: Exchange Code for Access Token")
        print("-" * 50)
        print("Replace YOUR_CLIENT_ID, YOUR_CLIENT_SECRET, and AUTHORIZATION_CODE:")
        print()

        token_curl = f'''curl -X POST "{token_endpoint}" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Authorization: Basic $(echo -n 'YOUR_CLIENT_ID:YOUR_CLIENT_SECRET' | base64)" \\
  -d "grant_type=authorization_code&code=AUTHORIZATION_CODE&redirect_uri=http://localhost:8080/callback"'''

        print(token_curl)
        print()
        print("This will return a JSON response with 'access_token'. Copy this token.")
        print()

        print("🔧 Step 4: Create Federation Policy")
        print("-" * 50)
        print("Replace YOUR_ACCESS_TOKEN with the token from step 3:")
        print()

        # Federation policy data
        policy_data = {
            "oidc_policy": {
                "issuer": config.get_issuer_url(),
                "audiences": [config.audience],
                "subject_claim": config.subject_claim
            }
        }

        policy_curl = f'''curl -X POST "{policy_endpoint}" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(policy_data, indent=2)}\''''

        print(policy_curl)
        print()

        print("🔧 Step 5: Verify Federation Policy")
        print("-" * 50)
        print("List existing policies to verify creation:")
        print()

        list_curl = f'''curl -X GET "{policy_endpoint}" \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json"'''

        print(list_curl)
        print()

        print("💡 Important Notes:")
        print("-" * 50)
        print("• PAT tokens cannot be used for federation policy management")
        print("• You must use OAuth access tokens with 'all-apis' scope")
        print("• The OAuth application must be created by a Databricks admin")
        print("• Account-level federation requires account admin permissions")
        print("• Workspace-level federation requires workspace admin permissions")
        print()

        print("🎯 Expected Success Response:")
        print("-" * 50)
        print('''{
  "policy_id": "********-1234-1234-1234-************",
  "oidc_policy": {
    "issuer": "''' + config.get_issuer_url() + '''",
    "audiences": ["''' + config.audience + '''"],
    "subject_claim": "''' + config.subject_claim + '''"
  }
}''')
        print()

        print("🚀 After successful creation, your U2M OAuth federation will be active!")

if __name__ == "__main__":
    generate_oauth_commands()
