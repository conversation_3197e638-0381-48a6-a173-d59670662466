<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Databricks Genie - Conversation</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Favicon -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.svg') }}" type="image/svg+xml">
</head>
<body class="bg-light">
    <!-- Header -->
    <header class="header">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="/">
                    <img src="{{ url_for('static', filename='img/databricks.png') }}" alt="Databricks Logo" class="mr-2" style="height: 45px; width: auto;">
                    <span class="font-weight-bold">Genie</span>
                </a>

                <div class="ml-auto d-flex align-items-center">
                    <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger btn-sm mr-3">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                    <img src="{{ url_for('static', filename='img/tudip.jpeg') }}" alt="Tudip Technologies Logo" style="height: 60px; width: auto;">
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-header bg-white border-bottom-0 py-3">
                            <h5 class="mb-0 font-weight-bold">Conversations</h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <button class="list-group-item list-group-item-action active d-flex align-items-center" style="background-color: #FF3621; border-color: #FF3621;">
                                    <i class="fas fa-comment-dots mr-3"></i>
                                    <div>
                                        <div class="font-weight-bold">Current Conversation</div>
                                        <small class="text-white">Started just now</small>
                                    </div>
                                </button>
                                <button class="list-group-item list-group-item-action d-flex justify-content-center py-3" id="new-conversation-btn">
                                    <i class="fas fa-plus mr-2"></i> New Conversation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main content -->
                <main class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                    <div class="card border-0 shadow-sm conversation-card">
                        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 font-weight-bold">
                                <i class="fas fa-comment-dots mr-2 text-primary"></i> Conversation
                            </h5>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary mr-2" id="clear-conversation-btn">
                                    <i class="fas fa-eraser mr-1"></i> Clear
                                </button>
                                <button class="btn btn-sm btn-outline-primary" id="export-conversation-btn">
                                    <i class="fas fa-download mr-1"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="conversation-container" id="conversation-container">
                                <!-- System welcome message -->
                                <div class="chat-message system-message">
                                    <div class="message-avatar">
                                        <img src="{{ url_for('static', filename='img/databricksicon.svg') }}" alt="Databricks Genie" style="width: 100%; height: 100%; object-fit: cover;">
                                    </div>
                                    <div class="message-content">
                                        <div class="message-header">
                                            <span class="message-sender">Databricks Genie</span>
                                            <span class="message-time">Just now</span>
                                        </div>
                                        <div class="message-body">
                                            <p>👋 Hello! I'm Databricks Genie, your AI assistant for data exploration.</p>
                                            <p>You can ask me questions about your data in natural language, and I'll help you find insights, generate visualizations, and create SQL queries.</p>
                                            <p>For example, try asking:</p>
                                            <ul>
                                                <li>"What were our top selling products last month?"</li>
                                                <li>"Show me the trend of daily active users over the past year"</li>
                                                <li>"What's the distribution of customer ages by region?"</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-white p-3">
                            <form id="message-form" class="d-flex align-items-end">
                                <div class="flex-grow-1 mr-3 position-relative">
                                    <textarea class="form-control" id="message-input" rows="1" placeholder="Ask a question about your data..." style="resize: none;"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </form>
                            <div class="mt-2">
                                <small class="text-muted d-flex align-items-center">
                                    <i class="fas fa-info-circle mr-1"></i> Press <span class="mx-1 font-weight-bold">Enter</span> to send, <span class="mx-1 font-weight-bold">Shift+Enter</span> for a new line
                                </small>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-4 bg-white border-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-left mb-3 mb-md-0">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <img src="{{ url_for('static', filename='img/databricks.png') }}" alt="Databricks Logo" class="mr-4" style="height: 45px; width: auto;">
                        <img src="{{ url_for('static', filename='img/tudip.jpeg') }}" alt="Tudip Technologies Logo" style="height: 60px; width: auto;">
                    </div>
                </div>
                <div class="col-md-6 text-center text-md-right">
                    <p class="text-muted mb-0">
                        <small>&copy; 2025 Databricks Genie API Client. All rights reserved.</small>
                    </p>
                    <p class="text-muted mb-0">
                        <small>Developed by Tudip Technologies Pvt Ltd</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Notification Container -->
    <div id="notification-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;"></div>

    <!-- JavaScript Dependencies -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM elements
            const conversationContainer = document.getElementById('conversation-container');
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');
            const clearConversationBtn = document.getElementById('clear-conversation-btn');
            const newConversationBtn = document.getElementById('new-conversation-btn');
            const exportConversationBtn = document.getElementById('export-conversation-btn');

            // Conversation state
            let conversationId = null;
            let messages = [];
            let messageHistory = [];
            let historyIndex = -1;

            // Focus the input field when the page loads
            messageInput.focus();

            // Auto-resize textarea
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Handle keyboard shortcuts
            messageInput.addEventListener('keydown', function(e) {
                // Enter to submit, Shift+Enter for new line
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    messageForm.dispatchEvent(new Event('submit'));
                }

                // Up arrow to navigate message history
                if (e.key === 'ArrowUp' && messageInput.value.trim() === '') {
                    e.preventDefault();
                    if (historyIndex < messageHistory.length - 1) {
                        historyIndex++;
                        messageInput.value = messageHistory[historyIndex];
                        // Move cursor to end of text
                        setTimeout(() => {
                            messageInput.selectionStart = messageInput.selectionEnd = messageInput.value.length;
                        }, 0);
                    }
                }

                // Down arrow to navigate message history
                if (e.key === 'ArrowDown' && historyIndex >= 0) {
                    e.preventDefault();
                    historyIndex--;
                    if (historyIndex >= 0) {
                        messageInput.value = messageHistory[historyIndex];
                    } else {
                        messageInput.value = '';
                    }
                    // Move cursor to end of text
                    setTimeout(() => {
                        messageInput.selectionStart = messageInput.selectionEnd = messageInput.value.length;
                    }, 0);
                }

                // Escape to clear input
                if (e.key === 'Escape') {
                    e.preventDefault();
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                }
            });

            // Handle message submission
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const question = messageInput.value.trim();
                if (!question) return;

                // Add to message history
                messageHistory.unshift(question);
                if (messageHistory.length > 50) { // Limit history size
                    messageHistory.pop();
                }
                historyIndex = -1; // Reset history index

                // Add user message to UI
                addUserMessage(question);

                // Clear input and focus
                messageInput.value = '';
                messageInput.style.height = 'auto';
                messageInput.focus();

                // Send message to API
                if (conversationId) {
                    sendFollowUpMessage(conversationId, question);
                } else {
                    startConversation(question);
                }
            });

            // Clear conversation
            clearConversationBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear this conversation? This cannot be undone.')) {
                    conversationContainer.innerHTML = '';
                    conversationId = null;
                    messages = [];

                    // Add welcome message
                    const welcomeMessage = `
                        <div class="chat-message system-message">
                            <div class="message-avatar">
                                <img src="{{ url_for('static', filename='img/databricksicon.svg') }}" alt="Databricks Genie" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <span class="message-sender">Databricks Genie</span>
                                    <span class="message-time">Just now</span>
                                </div>
                                <div class="message-body">
                                    <p>👋 Hello! I'm Databricks Genie, your AI assistant for data exploration.</p>
                                    <p>You can ask me questions about your data in natural language, and I'll help you find insights, generate visualizations, and create SQL queries.</p>
                                    <p>For example, try asking:</p>
                                    <ul>
                                        <li>"What were our top selling products last month?"</li>
                                        <li>"Show me the trend of daily active users over the past year"</li>
                                        <li>"What's the distribution of customer ages by region?"</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                    conversationContainer.innerHTML = welcomeMessage;
                }
            });

            // New conversation
            newConversationBtn.addEventListener('click', function() {
                if (confirm('Start a new conversation? Your current conversation will still be accessible from the sidebar.')) {
                    conversationContainer.innerHTML = '';
                    conversationId = null;
                    messages = [];

                    // Add welcome message
                    const welcomeMessage = `
                        <div class="chat-message system-message">
                            <div class="message-avatar">
                                <img src="{{ url_for('static', filename='img/databricksicon.svg') }}" alt="Databricks Genie" style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                            <div class="message-content">
                                <div class="message-header">
                                    <span class="message-sender">Databricks Genie</span>
                                    <span class="message-time">Just now</span>
                                </div>
                                <div class="message-body">
                                    <p>👋 Hello! I'm Databricks Genie, your AI assistant for data exploration.</p>
                                    <p>You can ask me questions about your data in natural language, and I'll help you find insights, generate visualizations, and create SQL queries.</p>
                                    <p>For example, try asking:</p>
                                    <ul>
                                        <li>"What were our top selling products last month?"</li>
                                        <li>"Show me the trend of daily active users over the past year"</li>
                                        <li>"What's the distribution of customer ages by region?"</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                    conversationContainer.innerHTML = welcomeMessage;
                }
            });

            // Export conversation
            exportConversationBtn.addEventListener('click', function() {
                if (messages.length === 0) {
                    alert('No messages to export.');
                    return;
                }

                // Create export data
                const exportData = {
                    conversation_id: conversationId,
                    messages: messages,
                    exported_at: new Date().toISOString()
                };

                // Create download link
                const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
                const downloadAnchorNode = document.createElement('a');
                downloadAnchorNode.setAttribute("href", dataStr);
                downloadAnchorNode.setAttribute("download", "genie_conversation_" + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + ".json");
                document.body.appendChild(downloadAnchorNode);
                downloadAnchorNode.click();
                downloadAnchorNode.remove();
            });

            // Add user message to UI
            function addUserMessage(question) {
                const timestamp = new Date().toLocaleTimeString();

                const messageHtml = `
                    <div class="chat-message user-message">
                        <div class="message-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">You</span>
                                <span class="message-time">${timestamp}</span>
                            </div>
                            <div class="message-body">
                                <p>${question}</p>
                            </div>
                        </div>
                    </div>
                `;

                conversationContainer.insertAdjacentHTML('beforeend', messageHtml);

                // Add typing indicator
                addTypingIndicator();

                // Scroll to bottom
                conversationContainer.scrollTop = conversationContainer.scrollHeight;
            }

            // Add system message to UI
            function addSystemMessage(message, attachments) {
                // Remove typing indicator
                removeTypingIndicator();

                const timestamp = new Date().toLocaleTimeString();

                // Create the message container
                const messageDiv = document.createElement('div');
                messageDiv.className = 'chat-message system-message';

                // Create avatar
                const avatarDiv = document.createElement('div');
                avatarDiv.className = 'message-avatar';
                const avatarImg = document.createElement('img');
                avatarImg.src = "{{ url_for('static', filename='img/databricksicon.svg') }}";
                avatarImg.alt = 'Databricks Genie';
                avatarImg.style = 'width: 100%; height: 100%; object-fit: cover;';
                avatarDiv.appendChild(avatarImg);

                // Create message content container
                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';

                // Create message header
                const messageHeader = document.createElement('div');
                messageHeader.className = 'message-header';

                const messageSender = document.createElement('span');
                messageSender.className = 'message-sender';
                messageSender.textContent = 'Databricks Genie';

                const messageTime = document.createElement('span');
                messageTime.className = 'message-time';
                messageTime.textContent = timestamp;

                messageHeader.appendChild(messageSender);
                messageHeader.appendChild(messageTime);

                // Create message body
                const messageBody = document.createElement('div');
                messageBody.className = 'message-body';

                // Process content for SQL queries and formatting
                if (message.includes('SQL Query')) {
                    // Split by section headers (SQL Query, Query Description)
                    const sections = message.split(/\b(SQL Query|Query Description:)\b/);

                    // First item is text before any section
                    if (sections[0].trim()) {
                        const p = document.createElement('p');
                        p.textContent = sections[0].trim();
                        messageBody.appendChild(p);
                    }

                    // Process each section
                    for (let i = 1; i < sections.length; i += 2) {
                        if (i < sections.length) {
                            const sectionTitle = sections[i].trim();
                            const sectionContent = sections[i+1] || '';

                            // Create section header
                            const header = document.createElement('h5');

                            // Use different styling for Query Description
                            if (sectionTitle === 'Query Description:') {
                                header.className = 'query-description-header';
                                header.innerHTML = '<strong>Query Description:</strong>';
                            } else {
                                // SQL Query header styling
                                header.className = 'sql-query-header';
                                header.textContent = sectionTitle;
                            }

                            messageBody.appendChild(header);

                            // Process section content for code blocks
                            if (sectionContent.includes('```')) {
                                const codeParts = sectionContent.split(/```([\s\S]*?)```/);
                                for (let j = 0; j < codeParts.length; j++) {
                                    if (j % 2 === 0) {
                                        // Regular text
                                        if (codeParts[j].trim()) {
                                            const p = document.createElement('p');
                                            p.textContent = codeParts[j].trim();
                                            messageBody.appendChild(p);
                                        }
                                    } else {
                                        // Code block
                                        const codeBlock = document.createElement('div');
                                        codeBlock.className = 'code-block';

                                        const pre = document.createElement('pre');
                                        const code = document.createElement('code');

                                        // Check if this is SQL
                                        if (sectionTitle.toLowerCase().includes('sql')) {
                                            code.className = 'language-sql';
                                        }

                                        code.textContent = codeParts[j].trim();
                                        pre.appendChild(code);
                                        codeBlock.appendChild(pre);

                                        messageBody.appendChild(codeBlock);
                                    }
                                }
                            } else {
                                // Simple text for this section
                                const p = document.createElement('p');
                                p.textContent = sectionContent.trim();

                                // Style the Query Description content
                                if (sectionTitle === 'Query Description:') {
                                    p.className = 'query-description-content';
                                }

                                messageBody.appendChild(p);
                            }
                        }
                    }
                } else if (message.includes('```')) {
                    // Standard code block processing
                    const parts = message.split(/```([\s\S]*?)```/);
                    for (let i = 0; i < parts.length; i++) {
                        if (i % 2 === 0) {
                            // Regular text
                            if (parts[i].trim()) {
                                const p = document.createElement('p');
                                p.textContent = parts[i].trim();
                                messageBody.appendChild(p);
                            }
                        } else {
                            // Code block
                            const codeBlock = document.createElement('div');
                            codeBlock.className = 'code-block';

                            const pre = document.createElement('pre');
                            const code = document.createElement('code');

                            // Check if language is specified
                            if (parts[i].startsWith('sql')) {
                                code.className = 'language-sql';
                                code.textContent = parts[i].substring(3).trim();
                            } else {
                                code.textContent = parts[i].trim();
                            }

                            pre.appendChild(code);
                            codeBlock.appendChild(pre);

                            messageBody.appendChild(codeBlock);
                        }
                    }
                } else {
                    // Simple text
                    const p = document.createElement('p');
                    p.textContent = message;
                    messageBody.appendChild(p);
                }

                // Add attachments if any
                if (attachments && attachments.length > 0) {
                    for (const attachment of attachments) {
                        if (attachment.type === 'query') {
                            const queryAttachment = document.createElement('div');
                            queryAttachment.className = 'query-attachment';

                            const queryHeader = document.createElement('div');
                            queryHeader.className = 'query-header';
                            queryHeader.innerHTML = '<h6 class="mb-0" style="color: #333;"><i class="fas fa-database mr-2" style="color: #FF3621;"></i> SQL Query</h6>';

                            const codeBlock = document.createElement('div');
                            codeBlock.className = 'code-block';

                            const pre = document.createElement('pre');
                            const code = document.createElement('code');
                            code.className = 'language-sql';
                            code.textContent = attachment.content;

                            pre.appendChild(code);
                            codeBlock.appendChild(pre);

                            const queryDescription = document.createElement('div');
                            queryDescription.className = 'query-description';
                            queryDescription.innerHTML = `<strong style="font-weight: 700; color: #333; font-size: 1.05rem;">Query Description:</strong> ${attachment.description || 'No description available'}`;

                            queryAttachment.appendChild(queryHeader);
                            queryAttachment.appendChild(codeBlock);
                            queryAttachment.appendChild(queryDescription);

                            messageBody.appendChild(queryAttachment);
                        }
                    }
                }

                // Assemble the message
                messageContent.appendChild(messageHeader);
                messageContent.appendChild(messageBody);

                messageDiv.appendChild(avatarDiv);
                messageDiv.appendChild(messageContent);

                // Add to conversation
                conversationContainer.appendChild(messageDiv);

                // Initialize syntax highlighting
                document.querySelectorAll('pre code').forEach((block) => {
                    hljs.highlightElement(block);
                });

                // Scroll to bottom
                conversationContainer.scrollTop = conversationContainer.scrollHeight;
            }

            // Add typing indicator
            function addTypingIndicator() {
                const typingHtml = `
                    <div class="chat-message system-message typing-indicator-message">
                        <div class="message-avatar">
                            <img src="{{ url_for('static', filename='img/databricksicon.svg') }}" alt="Databricks Genie" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-sender">Databricks Genie</span>
                                <span class="message-time">Typing...</span>
                            </div>
                            <div class="message-body">
                                <div class="typing-indicator">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                conversationContainer.insertAdjacentHTML('beforeend', typingHtml);
                conversationContainer.scrollTop = conversationContainer.scrollHeight;
            }

            // Remove typing indicator
            function removeTypingIndicator() {
                const typingIndicator = document.querySelector('.typing-indicator-message');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }

            // Start a new conversation
            function startConversation(question) {
                fetch('/api/start-conversation', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ question: question, content: question })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        removeTypingIndicator();
                        let errorMessage = `<div class="alert alert-danger">
                            <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                            <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                            <div class="mt-2">
                                <small class="text-muted">Technical details:</small>
                                <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${data.error}</pre>
                            </div>
                        </div>`;
                        addSystemMessage(errorMessage);
                        return;
                    }

                    // Remove typing indicator
                    removeTypingIndicator();

                    // Store conversation ID
                    conversationId = data.conversation_id;

                    // Add the response directly since we're using server-side wait_for_message
                    if (data.success) {
                        // Add message to state
                        messages.push({
                            id: data.message_id,
                            content: data.response,
                            timestamp: new Date().toISOString()
                        });

                        // Add message to UI
                        addSystemMessage(data.response);
                    }
                })
                .catch(error => {
                    console.error('Error starting conversation:', error);
                    removeTypingIndicator();
                    let errorMessage = `<div class="alert alert-danger">
                        <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                        <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                        <div class="mt-2">
                            <small class="text-muted">Technical details:</small>
                            <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${error.message}</pre>
                        </div>
                    </div>`;
                    addSystemMessage(errorMessage);
                });
            }

            // Send a follow-up message
            function sendFollowUpMessage(conversationId, question) {
                fetch('/api/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ conversation_id: conversationId, question: question, content: question })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        removeTypingIndicator();
                        let errorMessage = `<div class="alert alert-danger">
                            <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                            <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                            <div class="mt-2">
                                <small class="text-muted">Technical details:</small>
                                <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${data.error}</pre>
                            </div>
                        </div>`;
                        addSystemMessage(errorMessage);
                        return;
                    }

                    // Remove typing indicator
                    removeTypingIndicator();

                    // Add the response directly since we're using server-side wait_for_message
                    if (data.success) {
                        // Add message to state
                        messages.push({
                            id: data.message_id,
                            content: data.response,
                            timestamp: new Date().toISOString()
                        });

                        // Add message to UI
                        addSystemMessage(data.response);
                    }
                })
                .catch(error => {
                    console.error('Error sending follow-up message:', error);
                    removeTypingIndicator();
                    let errorMessage = `<div class="alert alert-danger">
                        <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                        <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                        <div class="mt-2">
                            <small class="text-muted">Technical details:</small>
                            <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${error.message}</pre>
                        </div>
                    </div>`;
                    addSystemMessage(errorMessage);
                });
            }

            // Poll for message status with adaptive polling
            function pollMessageStatus(conversationId, messageId, attempts = 0, interval = 1000) {
                // Increase max attempts to 60 (up to 5 minutes with increasing intervals)
                if (attempts > 60) {
                    removeTypingIndicator();
                    let errorMessage = `<div class="alert alert-danger">
                        <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                        <p class="mb-1 mt-2">Timed out waiting for a response from the Genie API. Please try again later.</p>
                        <p class="mb-0 mt-1 small text-muted">This could be due to high server load or network connectivity issues.</p>
                    </div>`;
                    addSystemMessage(errorMessage);
                    return;
                }

                fetch(`/api/get-message?conversation_id=${conversationId}&message_id=${messageId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        removeTypingIndicator();
                        let errorMessage = `<div class="alert alert-danger">
                            <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                            <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                            <div class="mt-2">
                                <small class="text-muted">Technical details:</small>
                                <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${data.error}</pre>
                            </div>
                        </div>`;
                        addSystemMessage(errorMessage);
                        return;
                    }

                    if (data.status === 'COMPLETE' || data.status === 'COMPLETED') {
                        // Message is complete, add to UI
                        const content = data.content || 'No response content';
                        const attachments = data.attachments || [];

                        // Add message to state
                        messages.push({
                            id: messageId,
                            content,
                            attachments,
                            timestamp: new Date().toISOString()
                        });

                        // Add message to UI
                        addSystemMessage(content, attachments);
                    } else {
                        // Message is still processing, poll again with adaptive interval
                        // Calculate next interval: increase by 20% each time, max 5 seconds
                        const nextInterval = Math.min(interval * 1.2, 5000);

                        setTimeout(() => {
                            pollMessageStatus(conversationId, messageId, attempts + 1, nextInterval);
                        }, interval);
                    }
                })
                .catch(error => {
                    console.error('Error polling message status:', error);
                    removeTypingIndicator();
                    let errorMessage = `<div class="alert alert-danger">
                        <strong><i class="fas fa-exclamation-triangle mr-2"></i> Error connecting to Databricks Genie API</strong>
                        <p class="mb-1 mt-2">There was an error processing your request. Please check your credentials and try again.</p>
                        <div class="mt-2">
                            <small class="text-muted">Technical details:</small>
                            <pre class="mt-1 p-2 bg-light" style="font-size: 0.8rem; border-radius: 4px;">${error.message}</pre>
                        </div>
                    </div>`;
                    addSystemMessage(errorMessage);
                });
            }
        });
    </script>
</body>
</html>
