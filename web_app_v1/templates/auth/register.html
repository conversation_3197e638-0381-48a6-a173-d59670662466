{% extends "base.html" %}

{% block title %}Register - Databricks Genie{% endblock %}

{% block content %}
<div class="container-fluid" style="background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); min-height: 100vh; padding: 2rem 0;">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 col-lg-5">
            <!-- Registration Card -->
            <div class="card border-0 shadow-lg">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <img src="{{ url_for('static', filename='img/databricks.png') }}" alt="Databricks" style="height: 40px;" class="mb-3">
                        <h3 class="font-weight-bold" style="color: #1B3139;">Create Account</h3>
                        <p class="text-muted">Join the Databricks Genie platform</p>
                    </div>

                    <!-- Flash Messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" class="needs-validation" novalidate>
                        {{ form.hidden_tag() }}

                        <div class="form-group">
                            {{ form.email.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                </div>
                                {{ form.email(class="form-control", placeholder="Enter your email address") }}
                            </div>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.databricks_username.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                {{ form.databricks_username(class="form-control", placeholder="Leave empty to use email") }}
                            </div>
                            <small class="form-text text-muted">Optional: Your Databricks username (defaults to email)</small>
                            {% if form.databricks_username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.databricks_username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.password.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                {{ form.password(class="form-control", placeholder="Enter your password") }}
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="fas fa-eye" id="toggleIcon1"></i>
                                    </button>
                                </div>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.confirm_password.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                {{ form.confirm_password(class="form-control", placeholder="Confirm your password") }}
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                        <i class="fas fa-eye" id="toggleIcon2"></i>
                                    </button>
                                </div>
                            </div>
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group mb-4">
                            {{ form.submit(class="btn btn-block py-2", style="background-color: #FF3621; color: white; font-weight: 500; border: none;") }}
                        </div>

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-muted">Already have an account? 
                                <a href="{{ url_for('auth.login') }}" style="color: #FF3621;">Sign in here</a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- System Info Card -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-body text-center py-3">
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="mr-3">
                            <i class="fas fa-shield-alt text-success"></i>
                            <small class="text-muted ml-1">Secure Registration</small>
                        </div>
                        <div class="mr-3">
                            <i class="fas fa-user-plus text-info"></i>
                            <small class="text-muted ml-1">Self-Service</small>
                        </div>
                        <div>
                            <i class="fas fa-check-circle text-primary"></i>
                            <small class="text-muted ml-1">Instant Access</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'toggleIcon1' : 'toggleIcon2');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.form-control:focus {
    border-color: #FF3621;
    box-shadow: 0 0 0 0.2rem rgba(255, 54, 33, 0.25);
}

.btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 54, 33, 0.25);
}

a {
    transition: color 0.3s ease;
}

a:hover {
    text-decoration: none;
    color: #e02e1b !important;
}

.alert {
    border: none;
    border-radius: 8px;
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card-body {
        padding: 2rem 1.5rem;
    }
}
</style>
{% endblock %}
