{% extends "base.html" %}

{% block title %}Databricks Genie - U2M OAuth Authentication{% endblock %}

{% block content %}
<div class="container" style="min-height: 70vh;">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Login Form Card -->
            <div class="card border-0 shadow-lg rounded-lg mb-4">
                <div class="card-header text-white text-center py-4" style="background-color: #FF3621;">
                    <h4 class="mb-0 d-flex align-items-center justify-content-center">
                        <span style="font-family: 'Inter', sans-serif; font-weight: 600;">databricks</span>
                        <i class="fas fa-user-lock mx-2"></i> User OAuth Authentication
                    </h4>
                </div>
                <div class="card-body p-4">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <form method="POST" action="{{ url_for('login') }}" class="needs-validation" novalidate>
                        {{ form.hidden_tag() }}

                        <div class="form-group">
                            {{ form.host.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-server"></i></span>
                                </div>
                                {{ form.host(class="form-control", placeholder="e.g., dbc-123abc45-6def.cloud.databricks.com") }}
                            </div>
                            <small class="form-text text-muted">Your Databricks workspace URL without https://</small>
                            {% if form.host.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.host.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            {{ form.space_id.label(class="form-label", style="font-weight: 500; color: #1B3139;") }}
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-project-diagram"></i></span>
                                </div>
                                {{ form.space_id(class="form-control", placeholder="e.g., 12ab345cd6789000ef6a2fb844ba2d31?o=1234567890123456") }}
                            </div>
                            <small class="form-text text-muted">Found in the URL of your Genie space</small>
                            {% if form.space_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.space_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Account ID field removed for workspace-level authentication -->

                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                {{ form.remember(class="custom-control-input") }}
                                {{ form.remember.label(class="custom-control-label") }}
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            {{ form.submit(class="btn btn-block py-2", style="background-color: #FF3621; color: white; font-weight: 500; border: none;") }}
                        </div>

                        <!-- SSO Login Options -->
                        <div class="mb-3">
                            <div class="text-center mb-2">
                                <small class="text-muted">Or sign in with</small>
                            </div>
                            <a href="{{ url_for('okta_auth.login') }}" class="btn btn-outline-primary btn-block">
                                <i class="fab fa-okta mr-2"></i> Sign in with Okta
                            </a>
                        </div>

                        <!-- Help Section -->
                        <div class="text-center">
                            <button class="btn btn-link text-muted" type="button" data-toggle="collapse" data-target="#helpSection" aria-expanded="false" aria-controls="helpSection">
                                <i class="fas fa-question-circle mr-1"></i> Need Help?
                            </button>
                        </div>

                        <div class="collapse" id="helpSection">
                            <div class="card card-body mt-3" style="background-color: #f8f9fa; border: 1px solid #e9ecef;">
                                <div class="mb-3">
                                    <h6 class="mb-2" style="color: #FF3621;">
                                        <i class="fas fa-user-shield mr-2"></i> About User-to-Machine (U2M) OAuth
                                    </h6>
                                    <p class="text-muted small mb-0">
                                        U2M OAuth allows you to authenticate with your personal Databricks user account. This preserves your individual identity in audit logs, making it easier to track who made each query.
                                    </p>
                                </div>
                                <hr class="my-2">
                                <div>
                                    <h6 class="mb-2" style="color: #FF3621;">
                                        <i class="fas fa-info-circle mr-2"></i> How It Works
                                    </h6>
                                    <p class="text-muted small mb-2">
                                        After configuring your connection details:
                                    </p>
                                    <div class="bg-white p-2 rounded shadow-sm mb-2">
                                        <ol class="text-muted small mb-0" style="padding-left: 1.2rem;">
                                            <li class="mb-1">You'll be redirected to Databricks to log in with your user account</li>
                                            <li class="mb-1">Databricks will ask for your consent to access the Genie API</li>
                                            <li class="mb-1">You'll be redirected back to this application with secure access</li>
                                            <li class="mb-0">All queries will be made under your user identity</li>
                                        </ol>
                                    </div>
                                    <p class="text-muted small mb-0 d-flex align-items-center">
                                        <i class="fas fa-external-link-alt mr-2" style="color: #FF3621;"></i>
                                        For detailed instructions, see the <a href="https://docs.databricks.com/dev-tools/auth/oauth-u2m" target="_blank" class="ml-1" style="color: #FF3621;">Databricks U2M OAuth documentation</a>.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Manual OAuth Entry Card (only show if there's a pending OAuth session) -->
            {% if session.get('pending_oauth') %}
            <div class="card border-0 shadow-lg rounded-lg mt-3">
                <div class="card-header text-white text-center py-3" style="background-color: #1B3139;">
                    <h5 class="mb-0">
                        <i class="fas fa-keyboard mr-2"></i> Manual OAuth Code Entry
                    </h5>
                </div>
                <div class="card-body p-4 text-center">
                    <p class="text-muted mb-3">
                        If the OAuth redirect failed or you were redirected to a page that couldn't be reached,
                        you can manually enter the authorization code here.
                    </p>
                    <a href="{{ url_for('oauth_manual') }}" class="btn btn-outline-primary">
                        <i class="fas fa-key mr-2"></i> Enter Authorization Code Manually
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
