{% extends "base.html" %}

{% block title %}Organization Configuration - Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Administration</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin.config') }}">
                            <i class="fas fa-cog mr-2"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.users') }}">
                            <i class="fas fa-users mr-2"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.federation_setup_guide') }}">
                            <i class="fas fa-link mr-2"></i> Federation Setup
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.audit_logs') }}">
                            <i class="fas fa-list-alt mr-2"></i> Audit Logs
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Organization Configuration</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <a href="{{ url_for('admin.dashboard') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left mr-1"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Configuration Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-cog mr-2"></i> System Configuration
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('admin.config') }}">
                                {{ form.hidden_tag() }}

                                <!-- Databricks Configuration Section -->
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-database mr-2"></i> Databricks Configuration
                                </h5>

                                <div class="form-group">
                                    {{ form.databricks_host.label(class="form-label font-weight-bold") }}
                                    {{ form.databricks_host(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Your Databricks workspace URL without https:// (e.g., dbc-123abc45-6def.cloud.databricks.com)
                                    </small>
                                    {% if form.databricks_host.errors %}
                                        <div class="text-danger">
                                            {% for error in form.databricks_host.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    {{ form.databricks_account_id.label(class="form-label font-weight-bold") }}
                                    {{ form.databricks_account_id(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Optional: For account-level federation. Leave empty for workspace-level federation.
                                    </small>
                                    {% if form.databricks_account_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.databricks_account_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    {{ form.genie_space_id.label(class="form-label font-weight-bold") }}
                                    {{ form.genie_space_id(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Found in the URL of your Genie space (e.g., 12ab345cd6789000ef6a2fb844ba2d31?o=****************)
                                    </small>
                                    {% if form.genie_space_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.genie_space_id.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <hr class="my-4">

                                <!-- Identity Provider Configuration Section -->
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-shield-alt mr-2"></i> Identity Provider Configuration
                                </h5>

                                <div class="form-group">
                                    {{ form.issuer_url.label(class="form-label font-weight-bold") }}
                                    {{ form.issuer_url(class="form-control") }}
                                    <small class="form-text text-muted">
                                        The base URL for your identity provider (this application). Used in JWT tokens.
                                    </small>
                                    {% if form.issuer_url.errors %}
                                        <div class="text-danger">
                                            {% for error in form.issuer_url.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.audience.label(class="form-label font-weight-bold") }}
                                            {{ form.audience(class="form-control") }}
                                            <small class="form-text text-muted">
                                                Token audience (usually 'databricks')
                                            </small>
                                            {% if form.audience.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.audience.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.subject_claim.label(class="form-label font-weight-bold") }}
                                            {{ form.subject_claim(class="form-control") }}
                                            <small class="form-text text-muted">
                                                JWT claim for user identity (usually 'sub')
                                            </small>
                                            {% if form.subject_claim.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.subject_claim.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Okta Configuration Section -->
                                <h5 class="mb-3 text-primary">
                                    <i class="fab fa-okta mr-2"></i> Okta Identity Provider
                                </h5>
                                <p class="text-muted mb-3">
                                    Configure Okta as your external identity provider for U2M authentication.
                                    <a href="#" onclick="showOktaGuide()" class="text-primary">View Setup Guide</a>
                                </p>

                                <div class="form-group">
                                    {{ form.okta_domain.label(class="form-label font-weight-bold") }}
                                    {{ form.okta_domain(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Your Okta domain (e.g., https://your-company.okta.com)
                                    </small>
                                    {% if form.okta_domain.errors %}
                                        <div class="text-danger">
                                            {% for error in form.okta_domain.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.okta_client_id.label(class="form-label font-weight-bold") }}
                                            {{ form.okta_client_id(class="form-control") }}
                                            <small class="form-text text-muted">
                                                Client ID from your Okta application
                                            </small>
                                            {% if form.okta_client_id.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.okta_client_id.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            {{ form.okta_client_secret.label(class="form-label font-weight-bold") }}
                                            {{ form.okta_client_secret(class="form-control", type="password") }}
                                            <small class="form-text text-muted">
                                                Client secret from your Okta application
                                            </small>
                                            {% if form.okta_client_secret.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.okta_client_secret.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <!-- Security Configuration Section -->
                                <h5 class="mb-3 text-primary">
                                    <i class="fas fa-key mr-2"></i> Security Configuration
                                </h5>

                                <div class="form-group">
                                    <div class="custom-control custom-checkbox">
                                        {{ form.generate_keys(class="custom-control-input") }}
                                        {{ form.generate_keys.label(class="custom-control-label font-weight-bold") }}
                                    </div>
                                    <small class="form-text text-muted">
                                        Check this to generate a new RSA key pair for JWT signing.
                                        {% if config and config.private_key_pem %}
                                            Current key ID: <code>{{ config.key_id }}</code>
                                        {% else %}
                                            No keys configured yet.
                                        {% endif %}
                                    </small>
                                </div>

                                <div class="form-group mt-4">
                                    {{ form.submit(class="btn btn-primary btn-lg") }}
                                    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-secondary btn-lg ml-2">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Configuration Status -->
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Configuration Status</h6>
                        </div>
                        <div class="card-body">
                            {% if config %}
                            <div class="mb-3">
                                <h6 class="text-success">
                                    <i class="fas fa-check-circle mr-1"></i> Configuration Exists
                                </h6>
                                <p class="text-muted small">
                                    Last updated: {{ config.updated_at.strftime('%Y-%m-%d %H:%M') }}
                                </p>
                            </div>

                            <div class="mb-3">
                                <h6 class="{% if config.private_key_pem %}text-success{% else %}text-warning{% endif %}">
                                    <i class="fas fa-{% if config.private_key_pem %}check-circle{% else %}exclamation-triangle{% endif %} mr-1"></i>
                                    RSA Keys
                                </h6>
                                {% if config.private_key_pem %}
                                    <p class="text-muted small">Key ID: <code>{{ config.key_id }}</code></p>
                                {% else %}
                                    <p class="text-muted small">No RSA keys configured</p>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <h6 class="{% if config.federation_policy_configured %}text-success{% else %}text-warning{% endif %}">
                                    <i class="fas fa-{% if config.federation_policy_configured %}check-circle{% else %}exclamation-triangle{% endif %} mr-1"></i>
                                    Federation Policy
                                </h6>
                                {% if config.federation_policy_configured %}
                                    <p class="text-muted small">Policy ID: {{ config.federation_policy_id }}</p>
                                {% else %}
                                    <p class="text-muted small">
                                        Not configured in Databricks yet.
                                        <a href="{{ url_for('admin.federation_setup_guide') }}" class="text-primary">Setup Guide</a>
                                    </p>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <h6 class="{% if okta_config %}text-success{% else %}text-warning{% endif %}">
                                    <i class="fab fa-{% if okta_config %}okta{% else %}okta{% endif %} mr-1"></i>
                                    Okta Integration
                                </h6>
                                {% if okta_config %}
                                    <p class="text-muted small">
                                        Domain: {{ okta_config.domain }}<br>
                                        Client ID: {{ okta_config.client_id[:8] }}...
                                    </p>
                                {% else %}
                                    <p class="text-muted small">
                                        Not configured yet. Fill in Okta details above.
                                    </p>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="text-center">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h6 class="text-warning">No Configuration</h6>
                                <p class="text-muted small">
                                    Please fill out the form to configure the system.
                                </p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if config %}
                    <div class="card shadow mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <a href="{{ url_for('admin.federation_setup_guide') }}" class="btn btn-info btn-block mb-2">
                                <i class="fas fa-link mr-1"></i> Federation Setup Guide
                            </a>
                            <button class="btn btn-outline-primary btn-block" onclick="testConnection()">
                                <i class="fas fa-plug mr-1"></i> Test Connection
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function testConnection() {
    // TODO: Implement connection test
    alert('Connection test functionality will be implemented');
}

function showOktaGuide() {
    const guideContent = `
    <div class="modal fade" id="oktaGuideModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fab fa-okta mr-2"></i> Okta Setup Guide
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h6 class="text-primary">Step 1: Create Okta Application</h6>
                    <ol>
                        <li>Go to your Okta admin console: <code>https://your-domain.okta.com/admin</code></li>
                        <li>Navigate to <strong>Applications</strong> → <strong>Applications</strong></li>
                        <li>Click <strong>Create App Integration</strong></li>
                        <li>Select <strong>OIDC - OpenID Connect</strong></li>
                        <li>Choose <strong>Web Application</strong></li>
                    </ol>

                    <h6 class="text-primary mt-3">Step 2: Configure Application</h6>
                    <ul>
                        <li><strong>App name:</strong> Databricks Genie U2M</li>
                        <li><strong>Sign-in redirect URI:</strong> <code>http://localhost:5001/auth/external/okta/callback</code></li>
                        <li><strong>Sign-out redirect URI:</strong> <code>http://localhost:5001/auth/logout</code></li>
                    </ul>

                    <h6 class="text-primary mt-3">Step 3: Get Credentials</h6>
                    <p>After creating the app, copy these values to the form above:</p>
                    <ul>
                        <li><strong>Okta Domain:</strong> https://your-domain.okta.com</li>
                        <li><strong>Client ID:</strong> 0oa... (from app settings)</li>
                        <li><strong>Client Secret:</strong> Click "Show" and copy</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Production Note:</strong> For production deployment, update redirect URIs to use your actual domain instead of localhost.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <a href="/static/docs/Okta_Setup_Guide.md" target="_blank" class="btn btn-primary">
                        View Full Guide
                    </a>
                </div>
            </div>
        </div>
    </div>
    `;

    // Remove existing modal if any
    $('#oktaGuideModal').remove();

    // Add modal to body and show
    $('body').append(guideContent);
    $('#oktaGuideModal').modal('show');
}
</script>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, .1);
}

main {
    margin-left: 240px;
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        height: auto;
    }

    main {
        margin-left: 0;
    }
}
</style>
{% endblock %}
