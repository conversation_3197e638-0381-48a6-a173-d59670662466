{% extends "base.html" %}

{% block title %}User Management - Databricks Genie{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Administration</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.config') }}">
                            <i class="fas fa-cog mr-2"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin.users') }}">
                            <i class="fas fa-users mr-2"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.audit_logs') }}">
                            <i class="fas fa-list-alt mr-2"></i> Audit Logs
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">User Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ url_for('admin.create_user') }}" class="btn btn-success">
                        <i class="fas fa-user-plus mr-1"></i> Create User
                    </a>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">All Users</h6>
                </div>
                <div class="card-body">
                    {% if users %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Databricks Username</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.email }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                                            {{ user.role.title() }}
                                        </span>
                                    </td>
                                    <td>{{ user.databricks_username or '-' }}</td>
                                    <td>
                                        <span class="badge badge-{{ 'success' if user.is_active else 'secondary' }}">
                                            {{ 'Active' if user.is_active else 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else '-' }}</td>
                                    <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never' }}</td>
                                    <td>
                                        {% if user.id != current_user.id %}
                                        <form method="POST" action="{{ url_for('admin.toggle_user_status', user_id=user.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-{{ 'warning' if user.is_active else 'success' }}" 
                                                    onclick="return confirm('Are you sure you want to {{ 'deactivate' if user.is_active else 'activate' }} this user?')">
                                                {{ 'Deactivate' if user.is_active else 'Activate' }}
                                            </button>
                                        </form>
                                        {% else %}
                                        <span class="text-muted">Current User</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No users found</h5>
                        <p class="text-muted">Create your first user to get started.</p>
                        <a href="{{ url_for('admin.create_user') }}" class="btn btn-success">
                            <i class="fas fa-user-plus mr-1"></i> Create User
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.main {
    margin-left: 240px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    .main {
        margin-left: 0;
    }
}
</style>
{% endblock %}
