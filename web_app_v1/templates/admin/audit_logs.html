{% extends "base.html" %}

{% block title %}Audit Logs - Databricks Genie{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Administration</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.config') }}">
                            <i class="fas fa-cog mr-2"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.users') }}">
                            <i class="fas fa-users mr-2"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin.audit_logs') }}">
                            <i class="fas fa-list-alt mr-2"></i> Audit Logs
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Audit Logs</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group mr-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">Export</button>
                    </div>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Activity Log</h6>
                </div>
                <div class="card-body">
                    {% if logs.items %}
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Resource</th>
                                    <th>Details</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs.items %}
                                <tr>
                                    <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') if log.timestamp else '-' }}</td>
                                    <td>
                                        {% if log.user %}
                                            <span class="badge badge-{{ 'danger' if log.user.role == 'admin' else 'primary' }}">
                                                {{ log.user.email }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">System</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge badge-info">{{ log.action }}</span>
                                    </td>
                                    <td>{{ log.resource or '-' }}</td>
                                    <td>
                                        <span title="{{ log.details }}">
                                            {{ log.details[:50] }}{% if log.details and log.details|length > 50 %}...{% endif %}
                                        </span>
                                    </td>
                                    <td>{{ log.ip_address or '-' }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if logs.pages > 1 %}
                    <nav aria-label="Audit logs pagination">
                        <ul class="pagination justify-content-center">
                            {% if logs.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.audit_logs', page=logs.prev_num) }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in logs.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != logs.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('admin.audit_logs', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if logs.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.audit_logs', page=logs.next_num) }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No audit logs found</h5>
                        <p class="text-muted">System activity will appear here as users interact with the application.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.main {
    margin-left: 240px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    .main {
        margin-left: 0;
    }
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
