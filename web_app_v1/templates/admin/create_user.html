{% extends "base.html" %}

{% block title %}Create User - Databricks Genie{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="sidebar-sticky">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>Administration</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.config') }}">
                            <i class="fas fa-cog mr-2"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('admin.users') }}">
                            <i class="fas fa-users mr-2"></i> Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('admin.audit_logs') }}">
                            <i class="fas fa-list-alt mr-2"></i> Audit Logs
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Create New User</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left mr-1"></i> Back to Users
                    </a>
                </div>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="form-group">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger">
                                            {% for error in form.email.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    {{ form.databricks_username.label(class="form-label") }}
                                    {{ form.databricks_username(class="form-control") }}
                                    <small class="form-text text-muted">
                                        Leave blank to use email address as Databricks username
                                    </small>
                                    {% if form.databricks_username.errors %}
                                        <div class="text-danger">
                                            {% for error in form.databricks_username.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group">
                                    {{ form.role.label(class="form-label") }}
                                    {{ form.role(class="form-control") }}
                                    {% if form.role.errors %}
                                        <div class="text-danger">
                                            {% for error in form.role.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="form-group form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-user-plus mr-1"></i> Create User
                                    </button>
                                    <a href="{{ url_for('admin.users') }}" class="btn btn-secondary ml-2">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">User Creation Notes</h6>
                        </div>
                        <div class="card-body">
                            <h6>Default Password</h6>
                            <p class="text-muted">
                                A temporary password <code>TempPass123!</code> will be assigned to the new user.
                                The user should change this password on first login.
                            </p>

                            <h6>User Roles</h6>
                            <ul class="text-muted">
                                <li><strong>Admin:</strong> Full access to all features and configuration</li>
                                <li><strong>User:</strong> Access to conversation features only</li>
                            </ul>

                            <h6>Databricks Integration</h6>
                            <p class="text-muted">
                                The Databricks username will be used for U2M authentication and audit logging.
                                If not specified, the email address will be used.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #333;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.main {
    margin-left: 240px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
    .main {
        margin-left: 0;
    }
}
</style>
{% endblock %}
